"use client";

import { useState, useEffect } from "react";

export default function Loading() {
  const [showLoading, setShowLoading] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setShowLoading(true);
    }, 200);

    return () => clearTimeout(timer);
  }, []);

  if (!showLoading) {
    return null;
  }

  return (
    <div className="flex justify-center items-start h-screen">
      <div className="loading loading-bars loading-lg mt-40" />
    </div>
  );
}
