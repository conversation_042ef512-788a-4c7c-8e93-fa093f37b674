"use server";

import dayjs from "dayjs";
import { CandidateStatusEnum } from "../enums/CandidateStatusEnum";
import { RouteEnum } from "../enums/RouteEnum";
import { getStatusInfoFromPgsa } from "./getStatusInfoFromPgsa";
import { redirect } from "next/navigation";
import {
  getHashFromRedis,
  getUserInfoFromRedis,
  isSetMember,
} from "./redisHelper";
import { IUserSessionData } from "../interfaces/IUserSessionData";
import { updateCandidateStatus } from "./updateCandidateStatus";
import { StatusInfoEmptyError } from "./exceptionTypes";
import { initializeCandidate } from "./initializeCandidate";
import { IStatusInfo } from "../interfaces/IStatusInfo";
import { getAppInsightsServer } from "./appInsightsServer";
import { deauthorizeUserObjectRedis } from "./deauthorizeUserObjectRedis";
import { getClientIp } from "./getClientIp";

const pgsUrl = process.env.PGS_URL;
const telemetryClient = getAppInsightsServer();

// Funksjon for å omdirigere brukeren hvis de ikke er på den forventede ruten
function redirectIfNotOnRoute(currentRoute: RouteEnum, targetRoute: RouteEnum) {
  if (currentRoute !== targetRoute) {
    redirect(targetRoute);
  }
}

function resetIsAuthorized(statusInfo: IUserSessionData) {
  try {
    deauthorizeUserObjectRedis(statusInfo.candidateNumber);
  } catch (error) {}
}

// Hovedfunksjon for å sjekke brukerens tilgang og navigere basert på deres status
export async function checkAccessAndNavigate(
  currentRoute: RouteEnum,
  userSessionData: IUserSessionData
) {
  let userStatusFromPgsa: IStatusInfo | null,
    userInfoFromRedis: IUserSessionData;

  try {
    // Henter brukerinformasjon fra Redis og statusinformasjon fra PGSA parallelt
    [userInfoFromRedis, userStatusFromPgsa] = await Promise.all([
      getUserInfoFromRedis(
        userSessionData.userSessionId,
        userSessionData.candidateNumber
      ),
      getStatusInfoFromPgsa(userSessionData.userId),
    ]);

    // Initialiserer kandidaten hvis statusinformasjon ikke er tilgjengelig
    if (!userStatusFromPgsa) {
      const userInfoFromRedis = await getUserInfoFromRedis(
        userSessionData.userSessionId,
        userSessionData.candidateNumber
      );
      await initializeCandidate(userInfoFromRedis);
      userStatusFromPgsa = await getStatusInfoFromPgsa(userSessionData.userId);
    } else if (
      userStatusFromPgsa.Status === CandidateStatusEnum.IkkeInnlogget
    ) {
      // Oppdaterer kandidatens status hvis de ikke er innlogget
      await updateCandidateStatus(
        CandidateStatusEnum.IkkeInnlogget,
        CandidateStatusEnum.VenterPaaDagspassordIkkeAutentisert,
        userStatusFromPgsa.TestPartId,
        userStatusFromPgsa.CandidateNumber,
        userStatusFromPgsa.ExamGroupCode,
        userStatusFromPgsa.CandidateNumber,
        userStatusFromPgsa.UserId,
        await getClientIp()
      );
      userStatusFromPgsa = await getStatusInfoFromPgsa(userSessionData.userId);
    }

    // Kaster en feil hvis statusinformasjon fortsatt ikke er tilgjengelig etter oppdatering
    if (!userStatusFromPgsa) throw new StatusInfoEmptyError();

    // Oppdaterer Redis hvis brukerens autorisert-flagg er satt fra PGSA
    if (userInfoFromRedis.isAuthorized) {
      if (
        userStatusFromPgsa.Status ===
        CandidateStatusEnum.VenterPaaDagspassordIkkeAutentisert
      ) {
        await updateCandidateStatus(
          userStatusFromPgsa.Status,
          CandidateStatusEnum.InnloggetAutentisert,
          userStatusFromPgsa.TestPartId,
          userStatusFromPgsa.CandidateNumber,
          userStatusFromPgsa.ExamGroupCode,
          userStatusFromPgsa.CandidateNumber,
          userSessionData.userId,
          await getClientIp()
        );

        userStatusFromPgsa = await getStatusInfoFromPgsa(
          userSessionData.userId
        );
      }
    }
  } catch (error) {
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        component: "checkAccessAndNavigate",
        action: "InitializationAndAuthorization",
        candidateNumber: userSessionData.candidateNumber,
        userGuid: userSessionData.userSessionId,
        currentRoute,
      },
    });
    if (error instanceof Error) {
      throw new Error(
        `Feil under initialisering og autorisasjon av bruker, eller henting av brukerdata: ${error.message}.`
      );
    } else throw error;
  } finally {
  }
  if (!userStatusFromPgsa) throw new StatusInfoEmptyError();
  // Sjekker om eksamen har startet basert på starttidspunktet
  const examStarted = dayjs.utc() > dayjs.utc(userStatusFromPgsa.TestStartTime);

  const isBlocked = await isSetMember(
    userSessionData.userId,
    userStatusFromPgsa.SchoolId
  );

  if (
    isBlocked &&
    userStatusFromPgsa.Status !== CandidateStatusEnum.Levert &&
    userStatusFromPgsa.Status !== CandidateStatusEnum.LevertManuelt
  ) {
    redirectIfNotOnRoute(currentRoute, RouteEnum.IkkeTilgang);
  } else {
    // Utfører route navigering basert på brukerens status og autorisasjon
    switch (userStatusFromPgsa.Status) {
      case CandidateStatusEnum.InnloggetAutentisert:
      case CandidateStatusEnum.LastetOpp:
        if (!userInfoFromRedis.isAuthorized) {
          redirectIfNotOnRoute(currentRoute, RouteEnum.Hjem);
        } else if (!examStarted) {
          redirectIfNotOnRoute(currentRoute, RouteEnum.Klar);
        } else {
          if (
            currentRoute !== RouteEnum.Eksamensoppgave &&
            currentRoute !== RouteEnum.Levering
          ) {
            redirectIfNotOnRoute(currentRoute, RouteEnum.Eksamensoppgave);
          }
        }
        break;
      case CandidateStatusEnum.Levert:
      case CandidateStatusEnum.LevertManuelt:
        resetIsAuthorized(userInfoFromRedis);
        redirectIfNotOnRoute(currentRoute, RouteEnum.Kvittering);
        break;
      case CandidateStatusEnum.DokumentertFravaer:
      case CandidateStatusEnum.IkkeDokumentertFravaer:
        resetIsAuthorized(userInfoFromRedis);
        redirectIfNotOnRoute(currentRoute, RouteEnum.Fravaer);
        break;

      case CandidateStatusEnum.SkalLeverePaPapir:
      case CandidateStatusEnum.IkkeDokumentertFravaer:
        resetIsAuthorized(userInfoFromRedis);
        redirectIfNotOnRoute(currentRoute, RouteEnum.Fravaer);
        break;

      case CandidateStatusEnum.VenterPaaDagspassordIkkeAutentisert:
      case CandidateStatusEnum.IkkeInnlogget:
        redirectIfNotOnRoute(currentRoute, RouteEnum.Hjem);
        break;
      default:
        if (!userInfoFromRedis.isAuthorized) {
          redirectIfNotOnRoute(currentRoute, RouteEnum.Hjem);
        }
        break;
    }
  }
}
