import { MigrationInterface, QueryRunner } from "typeorm";

export class AddMonitorOperations1749206100000 implements MigrationInterface {
    name = 'AddMonitorOperations1749206100000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Insert new monitor operations
        await queryRunner.query(`
            INSERT INTO "Operation" ("OperationID", "Operasjonstype", "BeskrivelseMal") VALUES
            (100, 'DokumentertFravær', 'Fraværstatus satt til ''Dokumentert fravær''.'),
            (101, 'TilgangGitt', 'Kandidaten fikk tilgang fra kandidatmonitoren.'),
            (102, 'TilgangAvvist', 'Kandidaten sin tilgangsforespørsel ble avvist i kandidatmonitoren.'),
            (103, 'UdokumentertFravær', 'Fraværstatus satt til ''Udokumentert fravær''.'),
            (104, 'Frav<PERSON>rOpphevet', 'Fraværstatus opphevet.'),
            (105, 'SendesIPost', 'Satt som ''Sendes i posten'' i kandidatmonitoren.'),
            (106, 'SendesIPostDel1', 'Del 1 satt som ''Sendes i posten'' i kandidatmonitoren.'),
            (107, 'SendesIPostDel2', 'Del 2 satt som ''Sendes i posten'' i kandidatmonitoren.'),
            (108, 'IkkeSendesIPost', 'Satt som ''Ikke sendes i posten'' i kandidatmonitoren.'),
            (109, 'IkkeSendesIPostDel1', 'Del 1 satt som ''Ikke sendes i posten'' i kandidatmonitoren'),
            (110, 'IkkeSendesIPostDel2', 'Del 2 satt som ''Ikke sendes i posten'' i kandidatmonitoren'),
            (111, 'LevertDigitalt', 'Satt som ''Levert digitalt'' i kandidatmonitoren.'),
            (112, 'LevertDigitaltDel2', 'Del 2 satt som ''Levert digitalt'' i kandidatmonitoren.'),
            (113, 'ÅpnetForNyLeveringEksamen', 'Eksamen ble åpnet for ny levering.'),
            (114, 'ÅpnetForNyLevering', 'Del {partNumber} ble åpnet for ny levering.'),
            (115, 'Sperret', 'Kandidaten fikk digitalt tilgang sperret.'),
            (116, 'SperreOpphevet', 'Digital sperre ble opphevet.'),
            (117, 'StatusOppdatert', 'Oppdaterte kandidatstatus. Status endret fra {currentStatus} til {newStatus}'),
            (118, 'FilSlettet', 'Filen slettet.'),
            (119, 'FilLastetOpp', 'Fil lastet opp'),
            (120, 'FilInnlevert', 'Filen ble innlevert.'),
            (121, 'FilInnlevertDel1', 'Filen ble innlevert for del 1.'),
            (122, 'FilInnlevertDel2', 'Filen ble innlevert for del 2.'),
            (123, 'FilLastetNed', 'Lastet ned fil.'),
            (124, 'EksamensdelEndret', 'Filen fikk endret eksamensdel til del {partNumber}'),
            (125, 'FilSjekket', 'Filen markert som sjekket.'),
            (126, 'TilgangFjernet', 'Kandidaten fikk fjernet digital tilgang fra monitoren.'),
            (127, 'FilLastetOppGruppe', 'Fil lastet opp via gruppeopplasteren.'),
            (128, 'FilInnlevertGruppe', 'Filen ble innlevert via gruppeopplasteren.'),
            (129, 'FilInnlevertGruppeDel1', 'Filen ble innlevert via gruppeopplasteren for del 1.'),
            (130, 'FilInnlevertGruppeDel2', 'Filen ble innlevert via gruppeopplasteren for del 2.'),
            (131, 'FilSlettetGruppe', 'Filen ble slettet via gruppeopplasteren.'),
            (132, 'FilSlettetGruppeDel1', 'Filen ble slettet via gruppeopplasteren for del 1.'),
            (133, 'FilSlettetGruppeDel2', 'Filen ble slettet via gruppeopplasteren for del 2.'),
            (134, 'EksamensdelEndretGruppe', 'Filen fikk endret eksamensdel til del {partNumber} via gruppeopplasteren')
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Remove the new operations
        await queryRunner.query(`
            DELETE FROM "Operation" 
            WHERE "OperationID" >= 100 AND "OperationID" <= 134
        `);
    }
}