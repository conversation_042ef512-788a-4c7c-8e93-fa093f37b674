{
  "compilerOptions": {
    "target": "es2017",
    "lib": ["dom", "dom.iterable", "esnext"],
    "strictPropertyInitialization": false,
    "allowJs": true,
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "node",  // Changed from "bundler" to "node" for TypeORM compatibility
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "baseUrl": ".",  // Added baseUrl for proper path resolution
    "paths": {
      "@/*": ["./*"]
    }
  },
  "include": [
    "next-env.d.ts",
    "**/*.ts",
    "**/*.tsx",
    ".next/types/**/*.ts",
    "build/types/**/*.ts",
    "db/**/*.ts"  // Explicitly include database files
  ],
  "exclude": ["node_modules"],
  "ts-node": {  // Added ts-node configuration for CLI operations
    "compilerOptions": {
      "module": "commonjs"
    }
  }
}