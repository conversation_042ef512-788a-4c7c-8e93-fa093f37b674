import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateOperation123Description1749206900000 implements MigrationInterface {
    name = 'UpdateOperation123Description1749206900000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Update operation 123 description to use consistent "ble" format
        await queryRunner.query(`
            UPDATE "Operation"
            SET "BeskrivelseMal" = 'Filen ble lastet ned.'
            WHERE "OperationID" = 123;
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Revert to original description
        await queryRunner.query(`
            UPDATE "Operation"
            SET "BeskrivelseMal" = 'Lastet ned fil.'
            WHERE "OperationID" = 123;
        `);
    }
}
