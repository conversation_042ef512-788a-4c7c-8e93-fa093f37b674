import { MigrationInterface, QueryRunner } from "typeorm";

export class ConvertTimestampToDatetime21749205961300 implements MigrationInterface {
    
    public async up(queryRunner: QueryRunner): Promise<void> {
        // Convert Timestamp to DATETIME2
        await queryRunner.query(`
            ALTER TABLE AuditLog 
            ALTER COLUMN Timestamp DATETIME2
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Convert Timestamp back to DATETIME
        await queryRunner.query(`
            ALTER TABLE AuditLog 
            ALTER COLUMN Timestamp DATETIME
        `);
    }
}
