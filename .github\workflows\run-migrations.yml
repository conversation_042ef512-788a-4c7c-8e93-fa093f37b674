name: Run TypeORM Migrations

on:
  workflow_dispatch:
    inputs:
      environment:
        description: "Select environment to run migrations"
        required: true
        type: choice
        options:
          - Dev
          - Test
          - QA
          - Production
      action:
        description: "Select action to perform"
        required: true
        type: choice
        options:
          - show-pending
          - run-migrations
          - revert-last
      confirmation:
        description: 'Type "CONFIRM" to execute (required for run-migrations and revert-last)'
        required: false
        type: string

jobs:
  validate-input:
    runs-on: ubuntu-latest
    steps:
      - name: Validate confirmation for destructive actions
        if: (github.event.inputs.action == 'run-migrations' || github.event.inputs.action == 'revert-last') && github.event.inputs.confirmation != 'CONFIRM'
        run: |
          echo "❌ You must type 'CONFIRM' to execute migrations or revert operations"
          exit 1

  setup-environment:
    needs: validate-input
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment }}
    outputs:
      database-host: ${{ vars.DATABASE_HOST }}
      database-name: ${{ vars.DATABASE_NAME }}
    steps:
      - name: Setup environment info
        run: |
          echo "🌍 Environment: ${{ github.event.inputs.environment }}"
          echo "🗄️ Database: ${{ vars.DATABASE_NAME }}"
          echo "🏠 Host: ${{ vars.DATABASE_HOST }}"

  show-pending:
    needs: setup-environment
    if: github.event.inputs.action == 'show-pending'
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20.x"
          cache: "npm"

      - name: Install dependencies
        run: npm ci

      - name: Set environment variables
        run: |
          echo "DATABASE_HOST=${{ vars.DATABASE_HOST }}" >> $GITHUB_ENV
          echo "DATABASE_USERNAME=${{ secrets.DATABASE_USERNAME }}" >> $GITHUB_ENV
          echo "DATABASE_PASSWORD=${{ secrets.DATABASE_PASSWORD }}" >> $GITHUB_ENV
          echo "DATABASE_NAME=${{ vars.DATABASE_NAME }}" >> $GITHUB_ENV

      - name: Show migration status
        run: |
          echo "📋 Migration Status for ${{ github.event.inputs.environment }}"
          echo "Database: ${{ vars.DATABASE_NAME }}"
          echo "Host: ${{ vars.DATABASE_HOST }}"
          echo "----------------------------------------"
          $env = "${{ github.event.inputs.environment }}".ToLower()
          npm run migration:show:$env

  run-migrations:
    needs: setup-environment
    if: github.event.inputs.action == 'run-migrations'
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20.x"
          cache: "npm"

      - name: Install dependencies
        run: npm ci

      - name: Set environment variables
        run: |
          echo "DATABASE_HOST=${{ vars.DATABASE_HOST }}" >> $GITHUB_ENV
          echo "DATABASE_USERNAME=${{ secrets.DATABASE_USERNAME }}" >> $GITHUB_ENV
          echo "DATABASE_PASSWORD=${{ secrets.DATABASE_PASSWORD }}" >> $GITHUB_ENV
          echo "DATABASE_NAME=${{ vars.DATABASE_NAME }}" >> $GITHUB_ENV

      - name: Show pending migrations before execution
        run: |
          echo "📋 Pending migrations before execution:"
          echo "----------------------------------------"
          $env = "${{ github.event.inputs.environment }}".ToLower()
          npm run migration:show:$env

      - name: Run migrations
        run: |
          echo "🚀 Running migrations on ${{ github.event.inputs.environment }}"
          echo "Database: ${{ vars.DATABASE_NAME }}"
          echo "Host: ${{ vars.DATABASE_HOST }}"
          echo "----------------------------------------"
          $env = "${{ github.event.inputs.environment }}".ToLower()
          npm run migration:run:$env

      - name: Show migration status after execution
        run: |
          echo "✅ Migration status after execution:"
          echo "----------------------------------------"
          $env = "${{ github.event.inputs.environment }}".ToLower()
          npm run migration:show:$env

  revert-last:
    needs: setup-environment
    if: github.event.inputs.action == 'revert-last'
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20.x"
          cache: "npm"

      - name: Install dependencies
        run: npm ci

      - name: Set environment variables
        run: |
          echo "DATABASE_HOST=${{ vars.DATABASE_HOST }}" >> $GITHUB_ENV
          echo "DATABASE_USERNAME=${{ secrets.DATABASE_USERNAME }}" >> $GITHUB_ENV
          echo "DATABASE_PASSWORD=${{ secrets.DATABASE_PASSWORD }}" >> $GITHUB_ENV
          echo "DATABASE_NAME=${{ vars.DATABASE_NAME }}" >> $GITHUB_ENV

      - name: Show migration status before revert
        run: |
          echo "📋 Migration status before revert:"
          echo "----------------------------------------"
          $env = "${{ github.event.inputs.environment }}".ToLower()
          npm run migration:show:$env

      - name: Revert last migration
        run: |
          echo "⏪ Reverting last migration on ${{ github.event.inputs.environment }}"
          echo "Database: ${{ vars.DATABASE_NAME }}"
          echo "Host: ${{ vars.DATABASE_HOST }}"
          echo "----------------------------------------"
          $env = "${{ github.event.inputs.environment }}".ToLower()
          npm run migration:revert:$env

      - name: Show migration status after revert
        run: |
          echo "✅ Migration status after revert:"
          echo "----------------------------------------"
          $env = "${{ github.event.inputs.environment }}".ToLower()
          npm run migration:show:$env
