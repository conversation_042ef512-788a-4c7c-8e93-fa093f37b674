{"Navigation": {"ChangeLanguage": "<PERSON><PERSON> til bokmål", "ChangeLanguage2": "<PERSON>is siden på bokmål", "Logout": "Logg ut"}, "Autorisering": {"PictureStandingPerson": "B<PERSON>te av ståande person", "WelcomeMessage": "<PERSON><PERSON>, {displayName}", "NeedAccessToExam": "Du treng tilgang til eksamen", "AlreadyAccess": "Det er eksamensvaktene som gir tilgang. Du blir automatisk sendt vidare når du får tilgang.", "GotAccess": "Be om tilgang", "Send": "Send", "ExamInviligatorWritePassword": "Eksamensvakta kan skrive inn dagspassord her", "WriteDayPassword": "Skriv inn dagspassord", "NoAccessError": "Du har endå ikkje fått tilgang", "WrongPassword": "Passordet er feil", "FieldEmpty": "Feltet kan ikkje vera tom", "Error": "Uventa feil oppst<PERSON>tt", "ControlCode": "Kontrollkode", "MessageSent": "Eksamensvaktene er varsla.", "DontRefresh": "Ver vennleg og ikkje oppdater sidan før ein eksamensvakt har gitt deg tilgang.", "AccessDenied": "Tilgang a<PERSON>", "AccessDeniedMessage": "Forespurnaden din om tilgang til eksamen er avslått. Ta kontakt med ei eksamensvakt for meir informasjon.", "SendNewRequest": "Send ny forespurnad"}, "Kandidatinfo": {"CandidateNumber": "Kandidatnummer", "SubjectCode": "Fagkode"}, "Klar": {"WelcomeMessage": "<PERSON><PERSON>, {displayName}", "ReadyExamNotStarted": "Flott! No er du heilt klar, men eksamen har ikkje starta.", "ReadyExamTwoPartsNotStarted": "Flott! No er du heilt klar, men del 2 har ikkje starta.", "GoToExam": "Gå til eksamen", "PictureSittingPerson": "<PERSON><PERSON><PERSON> av sitjande person", "ExamStartsIn": "Eksamen startar om:", "ExamStartsInTwoParts": "Del 2 startar om:", "SendForward": "Me sendar deg automatisk vidare. Lykke til!", "Hours": "timar", "Minutes": "minutt", "Seconds": "sekund", "Hour": "time", "Minute": "minutt", "Second": "sekund", "day": "dag", "days": "dagar", "feil": "<PERSON><PERSON> feil har op<PERSON><PERSON><PERSON><PERSON>", "UnderOneMinute": "under eitt minutt", "Exam": "Eksamen", "ExamTime": "Eksamenstider"}, "Eksamensoppgave": {"WelcomeToExam": "Velkommen til eksamen, ", "DeliverExamIn": "Lever eksamen i ", "ExamOpenToDeliver": "Eksamen er open for levering", "DeliverExam": "Last opp svaret", "ExaminationPaper": "Eksamensoppgåve", "PreparationPaper": "Førebuingsoppgåver", "ErrorLoadingExercise": "Feil oppstod. Klarte ikkje å hente eksamensoppgåva", "Malform": "Målform", "Next": "Neste", "Previous": "<PERSON><PERSON><PERSON>", "DownloadAssignment": "Last ned op<PERSON>gå<PERSON>", "PreviewAssignment": "Førehandsvis oppgåve", "PreviewAssignmentNotAvailable": "Førehandsvisning ikkje tilgjengeleg for denne filtypen", "PreviewFailed": "Kunne ikkje henta oppgåva"}, "Levering": {"HeadingMessage": "Her lastar du opp filene dine", "Delete": "<PERSON><PERSON>", "DownLoad": "Last ned", "UploadDate": "Opplastingsdato", "Size": "Storleik", "ErrorLoadFiles": "Feil oppstod. Klarte ikkje å hente filene", "UploadFailed": "Opplastinga feila", "DownloadFailed": "Nedlasting feila", "DeleteFailed": "Slettinga feila", "NotUniqueFilname": "Filnamn finst frå før", "FilesizeTooLarge": "Fila er for stor", "FileNotAllowed": "Filtypen er ikkje støtta", "Checked": "<PERSON><PERSON><PERSON>", "NotChecked": "<PERSON>k<PERSON><PERSON> s<PERSON>", "CheckYourFiles": "<PERSON><PERSON><PERSON><PERSON> filene dine", "DragFilesHere": "Dra filene hit (maks 20 filer)....", "UploadInProgress": " Opplasting pågår...", "UploadLoadYourFiles": "Last opp filene dine", "Or": "eller", "UploadFileLabel": "Trykk her for å laste opp filene dine", "CheckFilesMessage": "<PERSON><PERSON>k<PERSON> at du har lastet opp korrekte filer ved å laste dem ned. Hvis du skal gjøre endringer før du leverer må du slette filen, gjøre endringene dine og så laste opp filen på nytt.  Hvis skolen har levert filer på dine vegne, vil du ikke kunne se disse her, men du vil kunne se og laste ned alle leverte filer på <link>kandidat.udir.no</link> i morgen.", "DeliverYourFiles": "Lever filene dine", "DeliverFiles": "Lever filene", "Tilbake": "Tilbake", "NoFilesDeliver": "Du har ingen filer å levera", "DownLoadCheckDeliver": "Du må laste ned og sjekke alle filene før du kan levera dei", "DuplicateNameError": "må slettast på grunn av duplikat filnamn", "FilesizeError": "må slettast på grunn av for stor storleik", "FiletypeNotAllowed": "må slettast på grunn av ugyldig filtype", "FileUploadError": "må slettast på grunn av feil ved opplasting", "ConfirmDeleteHeading": "Stadfest sletting", "ConfirmDeleteMessage": "<PERSON><PERSON> du sikker på at du vil sletta fila", "ConfirmDeleteConfirmBtn": "<PERSON><PERSON> fila", "Cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ConfirmationFileDelivery": "Gi siste stadfesting før levering", "ConfirmDelivery": "Stadfest levering", "IConfirm": "Eg stadfestar at", "FilesAreCorrect": "Filene mine er korrekte", "CannotEditAnswer": "Eg veit at eg ikkje kan endre svaret mitt etter levering", "EmptyFile": "Fila er tom", "TooManyFilesTitle": "Du har lasta opp for mange filer", "TooManyFilesMessage": "Du kan maksimalt lasta opp 20 filer. Ver vennleg og slett ein eller fleire filer før du lastar opp fleire."}, "Kvittering": {"ReceiptDeliveredExam": "Kvittering for levert eksamen", "DoNotClosePage": "Ikkje lukk sida eller logg ut før du har fått løyve frå ei eksamensvakt.", "CandidateNumber": "Kandidatnummer", "Fag": "Fag", "Status": "Status", "Submitted": "<PERSON><PERSON>", "ExamAnswersAvailable": "Om eitt døgn blir svaret ditt tilgjengeleg på kandidat.udir.no", "DeliveredFiles": "Dei leverte filene dine", "ErrorGettingFiles": "<PERSON><PERSON>, klarte ikkje <PERSON> henta fila(e)", "DownloadReciept": "Last ned kvi<PERSON>a", "MessageDeliveredOnPaper": "Kandidaten har levert på papir.", "Size": "Storleik"}, "Fravaer": {"AbsenceHeading": "Du er registrert med fråvær", "AbsenceRegistered": "Du er registrert med fråvær og har derfor ikkje tilgang til eksamen. Dersom dette er feil, ta kontakt med ei eksamensvakt for hjelp.", "TryAgain": "Når ei eksamensvakt har fjerna fråværet, kan du logge ut og deretter logge inn igjen."}, "Feil": {"PageNotFound": "Hmm, denne sida fann me ikkje..", "PageNotFoundText": "Sidan du prøvde å nå finst ikkje", "GoToHomePage": "Gå til framsida", "ErrorMessage": "Feilmelding", "HTTPGeneralError": "Beklagar noko gjekk gale. Prøv igjen seinare.", "TryAgain": "<PERSON><PERSON><PERSON><PERSON> ig<PERSON>n", "ErrorCode": "Feilkode", "ErrorType": "FeilType", "Time": "Klokkeslett", "Page": "Side", "Service": "Teneste"}, "IkkeTilgang": {"Header": "Du har ikkje digital tilgang til eksamen", "Message": "Du har mista digital tilgang til eksamen. Dersom dette er feil, ta kontakt med ei eksamensvakt for hjelp."}}