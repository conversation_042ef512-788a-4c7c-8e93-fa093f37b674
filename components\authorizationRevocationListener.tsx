"use client";
import { useEffect } from "react";
import { useSignalR } from "@/app/context/signalRContext";
import { SignalRMessageEnum } from "@/app/enums/SignalRMessageEnum";
import { usePathname, useRouter } from "next/navigation";
import { federatedlogout } from "@/app/lib/federatedLogout";

interface AuthorizationRevocationListenerProps {
  sessionId: string;
}

export function AuthorizationRevocationListener({
  sessionId,
}: AuthorizationRevocationListenerProps) {
  const { connection } = useSignalR();
  const pathname = usePathname();
  const router = useRouter();

  useEffect(() => {
    if (!connection) return;

    // Listen for access revoked messages
    connection.on(SignalRMessageEnum.AuthorizationRevoked, async (message) => {
      // Check if this session should be deauthorized
      if (message?.deauthorizedSessions?.includes(sessionId)) {
        console.log(
          `AuthorizationRevoked received for session ${sessionId}, logging out`
        );
        federatedlogout(sessionId);
      }
    });

    // Cleanup listener when component unmounts
    return () => {
      connection?.off(SignalRMessageEnum.AuthorizationRevoked);
    };
  }, [connection, pathname, sessionId]);

  return null;
}
