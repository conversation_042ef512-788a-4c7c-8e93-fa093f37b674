import type { Metadata } from "next";
import { Roboto } from "next/font/google";
import "./globals.css";
import NavBar from "@/components/navbar";
import { NextAuthProvider } from "../nextAuthProvider";
import { getServerSession } from "next-auth";
import { notFound } from "next/navigation";
import { locales } from "@/middleware";
import { getTranslations } from "next-intl/server";
import { OnlineServer } from "../onlineServer";
import { getUserSessionData } from "../lib/getUserSessionData";
import { Toaster } from "@/components/ui/toaster";
import { SignalRProvider } from "../context/signalRContext";
import { AppInsightsProvider } from "@/components/appInsightsProvider";
import { NextIntlClientProvider } from "next-intl";
import { getMessages } from "next-intl/server";


export const metadata: Metadata = {
  title: "PGS",
  description: "Udirs prøvegjennomføringssystem",
  icons: {
    icon: "/favicon.ico",
  },
};

const roboto = Roboto({
  weight: ["400", "100", "700", "300", "500", "900"],
  subsets: ["latin"],
  display: "swap",
});

import { ValidLocale } from "@/app/interfaces/ILocale";
import { AuthorizationRevocationListener } from "@/components/authorizationRevocationListener";

interface RootLayoutProps {
  children: React.ReactNode;
  params: Promise<{
    locale: ValidLocale;
  }>;
}

export default async function RootLayout(props: RootLayoutProps) {
  const params = await props.params;

  const {
    locale
  } = params;

  const {
    children
  } = props;

  const userSessionData = await getUserSessionData();
  const preferredLanguage = userSessionData.language;

  // Ensure locale is valid
  if (!locales.includes(locale)) {
    notFound();
  }

  const [t, messages] = await Promise.all([
    getTranslations("Navigation"),
    getMessages()
  ]);

  const navigationMessages = {
    ChangeLanguage2: t("ChangeLanguage2"),
    Logout: t("Logout"),
  };

  function renderEnvironmentBanner(): React.JSX.Element {
    const environment =
      process.env.PGS_URL?.match(/https:\/\/pgs-(\w+).udir.no/)?.[1] ?? "prod";

    if (
      environment === "dev" ||
      environment === "test" ||
      environment === "qa"
    ) {
      let bgColor = "";

      switch (environment) {
        case "dev":
          bgColor = "bg-cyan-400";
          break;
        case "test":
          bgColor = "bg-yellow-400";
          break;
        case "qa":
          bgColor = "bg-green-400";
          break;
      }

      return (
        <div
          className={`flex gap-10 justify-center ${bgColor} text-black col-span-full py-1 shadow-md mb-2 sticky top-0 z-50`}
        >
          <div className="flex gap-1">
            Miljø:
            <span className="font-semibold">
              {(
                process.env.PGS_URL?.match(
                  /https:\/\/pgs-(\w+).udir.no/
                )?.[1] ?? "local"
              ).toUpperCase()}
            </span>
          </div>
          <div className="flex gap-1">
            Versjon:
            <span className="font-semibold">{process.env.APP_VERSION}</span>
          </div>
        </div>
      );
    }

    return <></>;
  }

  return (
    <html lang={preferredLanguage || locale} data-theme="udir">
      <body className={roboto.className}>
        <NextAuthProvider session={await getServerSession()}>
          <AppInsightsProvider>
            <SignalRProvider>
              <NextIntlClientProvider locale={locale} messages={messages}>
                <AuthorizationRevocationListener
                  sessionId={userSessionData.userSessionId}
                />
                <OnlineServer
                  onlineServerUrl={process.env.ONLINESERVER_URL ?? ""}
                  sessionId={userSessionData.userSessionId}
                  userId={userSessionData.userId}
                  candidateNumber={userSessionData.candidateNumber}
                  candidateGroupCode={userSessionData.candidateGroupCode}
                />
                <div className="grid grid-cols-12">
                  {/* {renderEnvironmentBanner()} */}
                  {/*  <div className="col-span-full">
                    <OperationalMessage />
                  </div> */}
                  <nav className="col-span-full mx-2 sm:mx-8 lg:mx-20">
                    <NavBar
                      ChangeLanguage={navigationMessages.ChangeLanguage2}
                      Logout={navigationMessages.Logout}
                      locale={locale}
                      language={preferredLanguage}
                    />
                  </nav>

                  <div className="col-span-1" />

                  <div className="col-span-10">
                    <main>{children}</main>
                    <Toaster />
                  </div>
                  <div className="col-span-1"></div>
                </div>
              </NextIntlClientProvider>
            </SignalRProvider>
          </AppInsightsProvider>
        </NextAuthProvider>
      </body>
    </html>
  );
}
