import { Metadata } from "next";
import { ReactNode } from "react";

export const metadata: Metadata = {
  title: "PGS - Eksamensoppgave",
  description: "Last ned eksamensoppgave",
};

export default async function RootLayout({
  children,
}: {
  children: ReactNode;
}) {
 // const userSessionData = await getUserSessionData();
 // await checkAccessAndNavigate(RouteEnum.Eksamensoppgave, userSessionData);

  return (
    <>
      {children}
    </>
  );
}
