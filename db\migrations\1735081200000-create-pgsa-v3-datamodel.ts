import { MigrationInterface, QueryRunner } from "typeorm";

export class CreatePgsaV3Datamodel1735081200000 implements MigrationInterface {
    name = 'CreatePgsaV3Datamodel1735081200000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create Person table
        await queryRunner.query(`
            CREATE TABLE "Person" (
                "Fodselsnummer" varchar(11) NOT NULL,
                "Fornavn" varchar(255) NOT NULL,
                "Etternavn" varchar(255) NOT NULL,
                CONSTRAINT "PK_Person" PRIMARY KEY ("Fodselsnummer")
            )
        `);

        // Create Fagkodeeksamen table
        await queryRunner.query(`
            CREATE TABLE "Fagkodeeksamen" (
                "FagkodeeksamensID" varchar(255) NOT NULL,
                "Fagkode" varchar(10),
                "Variantkode" varchar(10),
                "Eksamensperiode" varchar(50),
                "Fagnavn" varchar(255),
                "Varighet" int,
                "Eksamensdato" datetime2,
                "Eksamenstid" varchar(50),
                "ErTestFagkode" bit,
                "CreatedDate" datetime2 NOT NULL DEFAULT GETDATE(),
                "ModifiedDate" datetime2 NOT NULL DEFAULT GETDATE(),
                CONSTRAINT "PK_Fagkodeeksamen" PRIMARY KEY ("FagkodeeksamensID")
            )
        `);

        // Create Kandidatgruppe table
        await queryRunner.query(`
            CREATE TABLE "Kandidatgruppe" (
                "Kandidatgruppekode" varchar(255) NOT NULL,
                "SkoleID" varchar(255),
                "FagkodeeksamensID" varchar(255),
                "Kandidatgruppenavn" varchar(255),
                "CreatedDate" datetime2 NOT NULL DEFAULT GETDATE(),
                "ModifiedDate" datetime2 NOT NULL DEFAULT GETDATE(),
                CONSTRAINT "PK_Kandidatgruppe" PRIMARY KEY ("Kandidatgruppekode"),
                CONSTRAINT "FK_Kandidatgruppe_Fagkodeeksamen" FOREIGN KEY ("FagkodeeksamensID") REFERENCES "Fagkodeeksamen"("FagkodeeksamensID")
                -- FK for SkoleID will be added when Skole table is created
            )
        `);

        // Create Kandidateksamen table
        await queryRunner.query(`
            CREATE TABLE "Kandidateksamen" (
                "KandidatpameldingsID" varchar(255) NOT NULL,
                "Fodselsnummer" varchar(11) NOT NULL,
                "Kandidatgruppekode" varchar(255),
                "Kandidatnummer" varchar(50),
                "Malform" varchar(10),
                "Oppmotstatus" varchar(50),
                "CreatedDate" datetime2 NOT NULL DEFAULT GETDATE(),
                "ModifiedDate" datetime2 NOT NULL DEFAULT GETDATE(),
                CONSTRAINT "PK_Kandidateksamen" PRIMARY KEY ("KandidatpameldingsID"),
                CONSTRAINT "FK_Kandidateksamen_Person" FOREIGN KEY ("Fodselsnummer") REFERENCES "Person"("Fodselsnummer"),
                CONSTRAINT "FK_Kandidateksamen_Kandidatgruppe" FOREIGN KEY ("Kandidatgruppekode") REFERENCES "Kandidatgruppe"("Kandidatgruppekode")
            )
        `);

        // Create Eksamensdel table
        await queryRunner.query(`
            CREATE TABLE "Eksamensdel" (
                "EksamensdelID" varchar(255) NOT NULL,
                "FagkodeeksamensID" varchar(255) NOT NULL,
                "EksamensdelType" varchar(50),
                "GjennomforingStart" datetime2,
                "GjennomforingStopp" datetime2,
                "Gjennomforingsystem" varchar(100),
                "Eksamensveiledning" nvarchar(max),
                "ErPlagiatkontroll" bit,
                "CreatedDate" datetime2 NOT NULL DEFAULT GETDATE(),
                "ModifiedDate" datetime2 NOT NULL DEFAULT GETDATE(),
                CONSTRAINT "PK_Eksamensdel" PRIMARY KEY ("EksamensdelID"),
                CONSTRAINT "FK_Eksamensdel_Fagkodeeksamen" FOREIGN KEY ("FagkodeeksamensID") REFERENCES "Fagkodeeksamen"("FagkodeeksamensID")
            )
        `);        // Create Besvarelsesdel table
        await queryRunner.query(`
            CREATE TABLE "Besvarelsesdel" (
                "BesvarelsesdelID" varchar(255) NOT NULL,
                "KandidatpameldingsID" varchar(255),
                "BesvarelsesdelType" varchar(50),
                "BesvarelsesdelStatus" varchar(50),
                "Gjennomforingmodus" varchar(50),
                "CreatedDate" datetime2 NOT NULL DEFAULT GETDATE(),
                "ModifiedDate" datetime2 NOT NULL DEFAULT GETDATE(),
                CONSTRAINT "PK_Besvarelsesdel" PRIMARY KEY ("BesvarelsesdelID"),
                CONSTRAINT "FK_Besvarelsesdel_Kandidateksamen" FOREIGN KEY ("KandidatpameldingsID") REFERENCES "Kandidateksamen"("KandidatpameldingsID")
            )
        `);

        // Create BesvarelsesfilMetadata table
        await queryRunner.query(`
            CREATE TABLE "BesvarelsesfilMetadata" (
                "BlobReferanseBesvarelsesfil" varchar(255) NOT NULL,
                "FileSize" bigint,
                "MimeType" varchar(100),
                "OriginalNavn" varchar(255),
                "StandardNavn" varchar(255),
                CONSTRAINT "PK_BesvarelsesfilMetadata" PRIMARY KEY ("BlobReferanseBesvarelsesfil")
            )
        `);

        // Create Besvarelsesfil table
        await queryRunner.query(`
            CREATE TABLE "Besvarelsesfil" (
                "BesvarelsesfilID" varchar(255) NOT NULL,
                "BlobReferanseBesvarelsesfil" varchar(255),
                "BesvarelsesfilStatus" varchar(50),
                "PlagiatStatus" varchar(50),
                "ErMetadataVasket" bit,
                "ErPdfKonvertert" bit,
                "LasteOppVia" varchar(50),
                "LevertVia" varchar(50),
                "LasteOppTimeStamp" datetime2,
                "LevertTimeStamp" datetime2,
                "LasteOppAv" varchar(255),
                "LevertAv" varchar(255),
                "CreatedDate" datetime2 NOT NULL DEFAULT GETDATE(),
                "ModifiedDate" datetime2 NOT NULL DEFAULT GETDATE(),
                "CreatedBy" varchar(255),
                "ModifiedBy" varchar(255),
                CONSTRAINT "PK_Besvarelsesfil" PRIMARY KEY ("BesvarelsesfilID"),
                CONSTRAINT "FK_Besvarelsesfil_Metadata" FOREIGN KEY ("BlobReferanseBesvarelsesfil") REFERENCES "BesvarelsesfilMetadata"("BlobReferanseBesvarelsesfil")
            )
        `);

        // Create BesvarelsesDelBesvarelsesfil table (junction table - no PK, only FKs)
        await queryRunner.query(`
            CREATE TABLE "BesvarelsesDelBesvarelsesfil" (
                "BesvarelsesdelID" varchar(255) NOT NULL,
                "BesvarelsesfilID" varchar(255) NOT NULL,
                "CreatedDate" datetime2 NOT NULL DEFAULT GETDATE(),
                CONSTRAINT "FK_BesvarelsesDelBesvarelsesfil_Besvarelsesdel" FOREIGN KEY ("BesvarelsesdelID") REFERENCES "Besvarelsesdel"("BesvarelsesdelID"),
                CONSTRAINT "FK_BesvarelsesDelBesvarelsesfil_Besvarelsesfil" FOREIGN KEY ("BesvarelsesfilID") REFERENCES "Besvarelsesfil"("BesvarelsesfilID")
            )
        `);

        // Create EksamensmaterielfilMetadata table
        await queryRunner.query(`
            CREATE TABLE "EksamensmaterielfilMetadata" (
                "BlobReferanseEksamensmateriel" varchar(255) NOT NULL,
                "FileSize" bigint,
                "MimeType" varchar(100),
                "Filnavn" varchar(255),
                CONSTRAINT "PK_EksamensmaterielfilMetadata" PRIMARY KEY ("BlobReferanseEksamensmateriel")
            )
        `);

        // Create Eksamensmateriel table
        await queryRunner.query(`
            CREATE TABLE "Eksamensmateriel" (
                "EksamensmaterielfilID" varchar(255) NOT NULL,
                "BlobReferanseEksamensmateriel" varchar(255),
                "Eksamensmaterielkategori" varchar(100),
                "EksamensmaterielMalform" varchar(10),
                CONSTRAINT "PK_Eksamensmateriel" PRIMARY KEY ("EksamensmaterielfilID"),
                CONSTRAINT "FK_Eksamensmateriel_Metadata" FOREIGN KEY ("BlobReferanseEksamensmateriel") REFERENCES "EksamensmaterielfilMetadata"("BlobReferanseEksamensmateriel")
            )
        `);

        // Create EksamensdelEksamensmateriel table (junction table - no PK, only FKs)
        await queryRunner.query(`
            CREATE TABLE "EksamensdelEksamensmateriel" (
                "EksamensdelID" varchar(255) NOT NULL,
                "EksamensmaterielfilID" varchar(255) NOT NULL,
                "CreatedDate" datetime2 NOT NULL DEFAULT GETDATE(),
                CONSTRAINT "FK_EksamensdelEksamensmateriel_Eksamensdel" FOREIGN KEY ("EksamensdelID") REFERENCES "Eksamensdel"("EksamensdelID"),
                CONSTRAINT "FK_EksamensdelEksamensmateriel_Eksamensmateriel" FOREIGN KEY ("EksamensmaterielfilID") REFERENCES "Eksamensmateriel"("EksamensmaterielfilID")
            )
        `);

        // Create indexes for better performance
        await queryRunner.query(`CREATE INDEX "IX_Kandidateksamen_Fodselsnummer" ON "Kandidateksamen" ("Fodselsnummer")`);
        await queryRunner.query(`CREATE INDEX "IX_Kandidateksamen_Kandidatgruppekode" ON "Kandidateksamen" ("Kandidatgruppekode")`);
        await queryRunner.query(`CREATE INDEX "IX_Kandidatgruppe_FagkodeeksamensID" ON "Kandidatgruppe" ("FagkodeeksamensID")`);
        await queryRunner.query(`CREATE INDEX "IX_Kandidatgruppe_SkoleID" ON "Kandidatgruppe" ("SkoleID")`);
        await queryRunner.query(`CREATE INDEX "IX_Fagkodeeksamen_Fagkode" ON "Fagkodeeksamen" ("Fagkode")`);        await queryRunner.query(`CREATE INDEX "IX_Eksamensdel_FagkodeeksamensID" ON "Eksamensdel" ("FagkodeeksamensID")`);
        await queryRunner.query(`CREATE INDEX "IX_Besvarelsesdel_KandidatpameldingsID" ON "Besvarelsesdel" ("KandidatpameldingsID")`);
        await queryRunner.query(`CREATE INDEX "IX_Besvarelsesfil_BlobReferanseBesvarelsesfil" ON "Besvarelsesfil" ("BlobReferanseBesvarelsesfil")`);
        await queryRunner.query(`CREATE INDEX "IX_Eksamensmateriel_BlobReferanseEksamensmateriel" ON "Eksamensmateriel" ("BlobReferanseEksamensmateriel")`);
        await queryRunner.query(`CREATE INDEX "IX_BesvarelsesDelBesvarelsesfil_BesvarelsesdelID" ON "BesvarelsesDelBesvarelsesfil" ("BesvarelsesdelID")`);
        await queryRunner.query(`CREATE INDEX "IX_BesvarelsesDelBesvarelsesfil_BesvarelsesfilID" ON "BesvarelsesDelBesvarelsesfil" ("BesvarelsesfilID")`);
        await queryRunner.query(`CREATE INDEX "IX_EksamensdelEksamensmateriel_EksamensdelID" ON "EksamensdelEksamensmateriel" ("EksamensdelID")`);
        await queryRunner.query(`CREATE INDEX "IX_EksamensdelEksamensmateriel_EksamensmaterielfilID" ON "EksamensdelEksamensmateriel" ("EksamensmaterielfilID")`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop indexes
        await queryRunner.query(`DROP INDEX "IX_EksamensdelEksamensmateriel_EksamensmaterielfilID" ON "EksamensdelEksamensmateriel"`);
        await queryRunner.query(`DROP INDEX "IX_EksamensdelEksamensmateriel_EksamensdelID" ON "EksamensdelEksamensmateriel"`);
        await queryRunner.query(`DROP INDEX "IX_BesvarelsesDelBesvarelsesfil_BesvarelsesfilID" ON "BesvarelsesDelBesvarelsesfil"`);
        await queryRunner.query(`DROP INDEX "IX_BesvarelsesDelBesvarelsesfil_BesvarelsesdelID" ON "BesvarelsesDelBesvarelsesfil"`);
        await queryRunner.query(`DROP INDEX "IX_Eksamensmateriel_BlobReferanseEksamensmateriel" ON "Eksamensmateriel"`);        await queryRunner.query(`DROP INDEX "IX_Besvarelsesfil_BlobReferanseBesvarelsesfil" ON "Besvarelsesfil"`);
        await queryRunner.query(`DROP INDEX "IX_Besvarelsesdel_KandidatpameldingsID" ON "Besvarelsesdel"`);
        await queryRunner.query(`DROP INDEX "IX_Eksamensdel_FagkodeeksamensID" ON "Eksamensdel"`);
        await queryRunner.query(`DROP INDEX "IX_Fagkodeeksamen_Fagkode" ON "Fagkodeeksamen"`);
        await queryRunner.query(`DROP INDEX "IX_Kandidatgruppe_SkoleID" ON "Kandidatgruppe"`);
        await queryRunner.query(`DROP INDEX "IX_Kandidatgruppe_FagkodeeksamensID" ON "Kandidatgruppe"`);
        await queryRunner.query(`DROP INDEX "IX_Kandidateksamen_Kandidatgruppekode" ON "Kandidateksamen"`);
        await queryRunner.query(`DROP INDEX "IX_Kandidateksamen_Fodselsnummer" ON "Kandidateksamen"`);

        // Drop tables in reverse order (to respect foreign key constraints)
        await queryRunner.query(`DROP TABLE "EksamensdelEksamensmateriel"`);
        await queryRunner.query(`DROP TABLE "Eksamensmateriel"`);
        await queryRunner.query(`DROP TABLE "EksamensmaterielfilMetadata"`);
        await queryRunner.query(`DROP TABLE "BesvarelsesDelBesvarelsesfil"`);
        await queryRunner.query(`DROP TABLE "Besvarelsesfil"`);
        await queryRunner.query(`DROP TABLE "BesvarelsesfilMetadata"`);
        await queryRunner.query(`DROP TABLE "Besvarelsesdel"`);
        await queryRunner.query(`DROP TABLE "Eksamensdel"`);
        await queryRunner.query(`DROP TABLE "Kandidateksamen"`);
        await queryRunner.query(`DROP TABLE "Kandidatgruppe"`);
        await queryRunner.query(`DROP TABLE "Fagkodeeksamen"`);
        await queryRunner.query(`DROP TABLE "Person"`);
    }
}