import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateOperationDescriptions1749206800000 implements MigrationInterface {
    name = 'UpdateOperationDescriptions1749206800000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Update operation descriptions to use consistent "ble" format
        await queryRunner.query(`
            UPDATE "Operation"
            SET "BeskrivelseMal" = CASE "OperationID"
                WHEN 119 THEN 'Filen ble lastet opp.'
                WHEN 118 THEN 'Filen ble slettet.'
                WHEN 125 THEN 'Filen ble markert som sjekket.'
            END
            WHERE "OperationID" IN (119, 118, 125);
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Revert to original descriptions
        await queryRunner.query(`
            UPDATE "Operation"
            SET "BeskrivelseMal" = CASE "OperationID"
                WHEN 119 THEN 'Fil lastet opp'
                WHEN 118 THEN 'Filen slettet.'
                WHEN 125 THEN 'Filen markert som sjekket.'
            END
            WHERE "OperationID" IN (119, 118, 125);
        `);
    }
}
