import { NextRequest, NextResponse } from "next/server";
import { getUserSessionData } from "@/app/lib/getUserSessionData";
import { IUserSessionData } from "@/app/interfaces/IUserSessionData";
import { getHashFromRedis } from "@/app/lib/redisHelper";

export async function GET(request: NextRequest) {
  try {
    const userSessionData: IUserSessionData = await getUserSessionData();
    if (!userSessionData?.userSessionId) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    const redisKey = `candidate:${userSessionData.candidateNumber}:${userSessionData.userSessionId}`;
    const isAuthorized = await getHashFromRedis(redisKey, "isAuthorized");

    return NextResponse.json({ isAuthorized: isAuthorized === "true" });
  } catch (error) {
    console.error("Error checking authorization:", error);
    return NextResponse.json(
      { error: "Internal Server Error checking authorization" },
      { status: 500 }
    );
  }
}
