import { getTranslations } from "next-intl/server";
import React from "react";

interface CandidateInfoProps {
  candidateNumber: string;
  subjectCode: string;
}

export default async function CandidateInfo({
  candidateNumber,
  subjectCode,
}: CandidateInfoProps) {
  const t = await getTranslations("Kandidatinfo");

  return (
    <>
      <div className="flex flex-col text-lg font-medium sm:flex-row sm:items-center text-info gap-2 sm:gap-12">
        <div>
          <span>{t("CandidateNumber")}</span>: <span>{candidateNumber}</span>
        </div>
        <div>
          <span>{t("SubjectCode")}</span>: <span>{subjectCode}</span>
        </div>
      </div>
    </>
  );
}
