import { User } from "next-auth";
import { ValidLocale } from "./ILocale";

export interface IUserSessionData extends User {
  name: string;
  uid: string;
  language: ValidLocale;
  userId: string;
  candidateNumber: string;
  candidateGroupCode: string;
  dayCode: string;
  isAuthorized?: boolean;
  userSessionId: string;
  isNewAuthentication: boolean;
  timeForSession?: string;
  ipAddress?: string;
  activeIp?: string;
}
