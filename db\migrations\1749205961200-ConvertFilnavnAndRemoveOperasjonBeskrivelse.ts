import { MigrationInterface, QueryRunner } from "typeorm";

export class ConvertFilnavnAndRemoveOperasjonBeskrivelse1749205961200 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        // Convert Filnavn to NVARCHAR(MAX) (safe operation - column always exists)
        await queryRunner.query(`
            ALTER TABLE AuditLog 
            ALTER COLUMN Filnavn NVARCHAR(MAX)
        `);

        // Drop OperasjonBeskrivelse column only if it exists
        // Some environments may not have this column if AuditLog was created without it
        const columnExists = await queryRunner.query(`
            SELECT COUNT(*) as count 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'AuditLog' 
            AND COLUMN_NAME = 'OperasjonBeskrivelse'
        `);

        if (columnExists[0].count > 0) {
            await queryRunner.query(`
                ALTER TABLE AuditLog
                DROP COLUMN OperasjonBeskrivelse
            `);
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Recreate OperasjonBeskrivelse column
        await queryRunner.query(`
            ALTER TABLE "AuditLog"
            ADD COLUMN "OperasjonBeskrivelse" VARCHAR(255)
        `);        // Convert Filnavn back to VARCHAR(255)
        await queryRunner.query(`
            ALTER TABLE AuditLog 
            ALTER COLUMN Filnavn VARCHAR(255)
        `);
    }
}
