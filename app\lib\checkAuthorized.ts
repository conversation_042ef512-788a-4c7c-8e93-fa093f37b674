"use server";

import { getStatusInfoFromPgsa } from "./getStatusInfoFromPgsa";
import { setHashInRedis, hashKeyExists } from "./redisHelper";
import { getUserSessionData } from "./getUserSessionData";
import { StatusInfoError } from "./exceptionTypes";
import { IStatusInfo } from "../interfaces/IStatusInfo";
import { sendSignalRStatusRequest } from "./sendSignalRStatusRequest";
import { deauthorizeUserObjectRedis } from "./deauthorizeUserObjectRedis";
import { getAppInsightsServer } from "./appInsightsServer";
import AuditLogService from "@/db/services/auditLogService";
import { getUserLoggingInfo } from "./getUserLoggingInfo";
import { OperationEnum } from "../enums/OperationEnum";
import { headers } from "next/headers";
import { sendAuditLogToServiceBus } from "./serviceBusClient";

const telemetryClient = getAppInsightsServer();

export async function checkAuthorized(prevState: any, formData: FormData) {
  const userSessionData = await getUserSessionData();

  try {
    // Hent statusinformasjon fra PGSA
    const statusInfo: IStatusInfo | null = await getStatusInfoFromPgsa(
      userSessionData.userId
    );
    if (!statusInfo) {
      throw new StatusInfoError("Kunne ikke hente statusinformasjon fra PGSA");
    }

    // Lagre forespørselen i Redis
    const key = `AccessRequest:${statusInfo.SchoolId}`;
    const requestData = JSON.stringify({
      id: userSessionData.userSessionId,
      candidateNumber: statusInfo.CandidateNumber,
      hasRequestedAccess: true,
      timestamp: new Date().toISOString(),
    });

    try {
      await setHashInRedis(key, statusInfo.CandidateNumber, requestData);
    } catch (error) {
      console.error("Error saving to Redis:", error);
      throw new Error("Kunne ikke lagre forespørselen i Redis");
    }

    // Deauthorize alle tidligere sesjoner før tilgangsbestilling sendes
    try {
      await deauthorizeUserObjectRedis(statusInfo.CandidateNumber);
    } catch (error) {
      console.error(
        "Failed to deauthorize previous sessions before access request:",
        error
      );
      telemetryClient?.trackException({
        exception: error as Error,
        properties: {
          component: "checkAuthorized",
          action: "deauthorizeBeforeAccessRequest",
          candidateNumber: statusInfo.CandidateNumber,
        },
      });
      // Fortsett med tilgangsbestilling selv om deauthorize feiler
    }

    // Send SignalR-forespørsel
    const requestId = await sendSignalRStatusRequest(
      statusInfo.SchoolId,
      statusInfo.CandidateNumber,
      true,
      userSessionData.userSessionId
    );

    const data = await getUserLoggingInfo(OperationEnum.TilgangBedt);

    if (!data) {
      console.error("Failed to build audit log data for access request");
    } else {
      
      try {
        const headersList = await headers();
        const userAgent = headersList.get("user-agent") || "Unknown User Agent";
        // Send to Service Bus
        await sendAuditLogToServiceBus(data, userAgent);
      } catch (error: any) {
        console.error("Error sending message to Service Bus:", error);
      }
    }

    return { requestId, error: "" };
  } catch (error) {
    console.error("Error in checkAuthorized:", error);

    // Logg feilen
    telemetryClient?.trackException({
      exception: error instanceof Error ? error : new Error(String(error)),
      properties: {
        userGuid: userSessionData.userSessionId,
        candidateNumber: userSessionData.candidateNumber,
      },
    });

    return {
      requestId: "",
      error: error instanceof Error ? error.message : "En uventet feil oppstod",
    };
  }
}
