import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateSpecificOperationTypes1749206300000 implements MigrationInterface {
    name = 'UpdateSpecificOperationTypes1749206300000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            UPDATE "Operation"
            SET "Operasjonstype" = CASE "OperationID"
                WHEN 103 THEN 'Autorisering'
                WHEN 104 THEN 'Autorisering'
                WHEN 105 THEN 'Innlevering'
                WHEN 106 THEN 'Innlevering'
                WHEN 107 THEN 'Innlevering'
                WHEN 108 THEN 'Innlevering'
                WHEN 109 THEN 'Innlevering'
                WHEN 110 THEN 'Innlevering'
            END
            WHERE "OperationID" BETWEEN 103 AND 110;
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            UPDATE "Operation"
            SET "Operasjonstype" = CASE "OperationID"
                WHEN 103 THEN 'UdokumentertFravær'
                WHEN 104 THEN 'FraværOpphevet'
                WHEN 105 THEN 'SendesIPost'
                WHEN 106 THEN 'SendesIPostDel1'
                WHEN 107 THEN 'SendesIPostDel2'
                WHEN 108 THEN 'IkkeSendesIPost'
                WHEN 109 THEN 'IkkeSendesIPostDel1'
                WHEN 110 THEN 'IkkeSendesIPostDel2'
            END
            WHERE "OperationID" BETWEEN 103 AND 110;
        `);
    }
}