export class AllowedFileTypesError extends Error {
  constructor(message = "Klarte ikke å hente data over tillatte filtyper") {
    super(message);
    this.name = "AllowedFileTypesError";
  }
}

export class SubjectCodeFileSizesError extends Error {
  constructor(
    message = "Klarte ikke å hente data over tillatte filstørrelser"
  ) {
    super(message);
    this.name = "SubjectCodeFileSizesError";
  }
}

export class GetExerciseError extends Error {
  constructor(message = "Klarte ikke å hente eksamensoppgavene for fagkoden") {
    super(message);
    this.name = "GetExerciseError";
  }
}

export class StatusInfoError extends Error {
  constructor(message = "Feil ved henting av statusinfo fra PGSA") {
    super(message);
    this.name = "StatusInfoError";
  }
}

export class UserSessionDataError extends Error {
  constructor(message = "Brukersesjon mangler data") {
    super(message);
    this.name = "UserSessionDataError";
  }
}

export class UserPgsaStatusInfoRedisError extends Error {
  constructor(message = "Finner ingen PGSA brukerdata i Redis") {
    super(message);
    this.name = "UserPgsaStatusInfoRedisError";
  }
}

export class UserInfoRedisError extends Error {
  constructor(message = "Finner ingen brukerdata i Redis") {
    super(message);
    this.name = "UserInfoRedisError";
  }
}

export class StatusUpdateError extends Error {
  constructor(message = "Feil oppdatering av status i PGSA") {
    super(message);
    this.name = "StatusUpdateError";
  }
}

export class StatusInfoEmptyError extends Error {
  constructor(message = "Kandidatstatus mangler data") {
    super(message);
    this.name = "StatusInfoEmptyError";
  }
}

export class NavigationError extends Error {
  constructor(message = "Feil i AccessAndNavigation") {
    super(message);
    this.name = "NavigationError";
  }
}
export class InitializeCandidateError extends Error {
  constructor(message = "Feil under initialisering av bruker") {
    super(message);
    this.name = "InitializeCandidateError";
  }
}
