import { Skeleton } from "@/components/ui/skeleton";
import { getTranslations } from "next-intl/server";
import React from "react";

const Loading = async () => {
  const t = await getTranslations("Kvittering");
  return (
    <>
      <div className="flex flex-col gap-6 mb-8">
        <Skeleton className="w-1/2 h-12 bg-base-300" />
      </div>
      <div className="flex flex-col w-full gap-8">
        <div key="receiptInfo">
          <div className="flex flex-col gap-6">
            <div className="lg:w-2/3 gap-8">
              <div className="card rounded-none w-full bg-white">
                <div className="card-body">
                  <Skeleton className="w-full h-16 bg-base-300 mb-4" />
                  <div className="sm:grid grid-cols-[130px_auto] gap-3">
                    <Skeleton className="w-24 h-5 bg-base-300" />
                    <Skeleton className="w-32 h-5 bg-base-300" />
                    <Skeleton className="w-24 h-5 bg-base-300" />
                    <Skeleton className="w-48 h-5 bg-base-300" />
                    <Skeleton className="w-24 h-5 bg-base-300" />
                    <Skeleton className="w-32 h-5 bg-base-300" />
                  </div>
                  <Skeleton className="w-64 h-5 bg-base-300 mt-6" />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div key="submittedFiles">
          <Skeleton className="w-48 h-8 bg-base-300 mb-6" />
          <Skeleton className="w-2/3 h-11 rounded-md bg-base-300" />
        </div>
        <div key="downloadReciept">
          <Skeleton className="w-48 h-12 rounded-md bg-base-300" />
        </div>
      </div>
    </>
  );
};

export default Loading;
