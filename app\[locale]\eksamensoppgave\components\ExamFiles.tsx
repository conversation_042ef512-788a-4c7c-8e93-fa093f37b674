import { getTranslations } from "next-intl/server";
import { getStatusInfoFromPgsa } from "@/app/lib/getStatusInfoFromPgsa";
import { getUserSessionData } from "@/app/lib/getUserSessionData";
import {
  GetExerciseError,
  StatusInfoEmptyError,
} from "@/app/lib/exceptionTypes";
import { getPgsaAccessToken } from "@/app/lib/getPgsaAccesstoken";
import { IStatusInfo } from "@/app/interfaces/IStatusInfo";
import { getValueFromRedis } from "@/app/lib/redisHelper";
import ExamComponent from "./ExamComponent";
import { getAppInsightsServer } from "@/app/lib/appInsightsServer";

const PgsaApiUrl = process.env.PGSA_API_ASYNC_URL;
const telemetryClient = getAppInsightsServer();

interface IExerciseResponse {
  Exercise: IExercise[];
  Preparation: IExercise[];
}

async function getExercise(statusInfo: IStatusInfo) {
  let response: Response | null = null;
  try {
    // Hent eksamensfiler fra Redis
    const valueFromRedis = await getValueFromRedis(
      `EE-${statusInfo.SubjectCode}-${
        statusInfo.Variant?.trim() ? statusInfo.Variant : 0
      }`
    );

    // Hvis eksamensfiler finnes i Redis, returner dem
    if (valueFromRedis) {
      return JSON.parse(valueFromRedis);
    }

    // Hvis ikke, generer token og hent eksamensfiler fra PgsaApiUrl
    const pgsaAccessToken = await getPgsaAccessToken();

    // Hent eksamensfiler fra PgsaApiUrl
    response = await fetch(
      `${PgsaApiUrl}/api/utils/exercise/${statusInfo.CandidateNumber}/${statusInfo.ExamGroupCode}/${statusInfo.SubjectCode}/${statusInfo.Variant}`,
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${pgsaAccessToken}`,
          "Content-Type": "application/json",
        },
        cache: "no-store",
      }
    );

    if (!response.ok) {
      throw new Error(
        `Klarte ikke å hente eksamensfiler for bruker. Feilkode: ${response.status}. Feilmelding: ${response.statusText}`
      );
    }
    return await response.json();
  } catch (error) {
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        component: "ExamFiles",
        function: "getExercise",
        statusCode: response?.status?.toString(),
        statusText: response?.statusText,
        candidateNumber: statusInfo.CandidateNumber,
        examGroupCode: statusInfo.ExamGroupCode,
        subjectCode: statusInfo.SubjectCode,
        variant: statusInfo.Variant,
        response: await response?.text(),
      },
      measurements: {
        timestamp: Date.now(),
      },
    });
    if (error instanceof Error) {
      console.error("Error in getExercise:", error.message);
      throw new GetExerciseError(error.message); // Inkluderer den opprinnelige feilmeldingen for klarhet
    } else {
      console.error("Error in getExercise: Unknown error");
      throw new GetExerciseError("Unknown error occurred in getExercise");
    }
  } finally {
  }
}

const ExamFiles = async () => {
  const userSessionData = await getUserSessionData();

  const [statusInfo, t] = await Promise.all([
    getStatusInfoFromPgsa(userSessionData.userId),
    getTranslations("Eksamensoppgave"),
  ]);

  if (!statusInfo) throw new StatusInfoEmptyError();

  let { Exercise, Preparation }: IExerciseResponse = {
    Exercise: [],
    Preparation: [],
  };
  const exerciseResponse = await getExercise(statusInfo);

  Exercise = exerciseResponse.Exercise;
  Preparation = exerciseResponse.Preparation;

  return (
    <div className="flex flex-col gap-4">
      {Preparation.map((prep, index) => (
        <div key={`preperationFile-${index}`}>
          <ExamComponent
            item={prep}
            paperType={t("PreparationPaper")}
            downloadtxt={t("DownloadAssignment")}
            previewTxt={t("PreviewAssignment")}
            malformTxt={t("Malform")}
            previewHoverLabel={t("PreviewAssignmentNotAvailable")}
            previousLabel={t("Previous")}
            nextLabel={t("Next")}
            errorLoadingExerciseLabel={t("ErrorLoadingExercise")}
            subjectCode={statusInfo.SubjectCode}
            testPartId={statusInfo.TestPartId}
            preparation={true}
          />
        </div>
      ))}

      {Exercise.map((exercise, index) => (
        <div key={`exerciseFile-${index}`}>
          <ExamComponent
            item={exercise}
            paperType={t("ExaminationPaper")}
            downloadtxt={t("DownloadAssignment")}
            previewTxt={t("PreviewAssignment")}
            malformTxt={t("Malform")}
            previewHoverLabel={t("PreviewAssignmentNotAvailable")}
            previousLabel={t("Previous")}
            nextLabel={t("Next")}
            errorLoadingExerciseLabel={t("ErrorLoadingExercise")}
            subjectCode={statusInfo.SubjectCode}
            testPartId={statusInfo.TestPartId}
            preparation={false}
          />
        </div>
      ))}
    </div>
  );
};

export default ExamFiles;
