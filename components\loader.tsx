import React from "react";
import { Skeleton } from "./ui/skeleton";

const Loader = ({ height = "40", width = "full", count = 1, gap = 4 }) => {
  const skeletonClass = `w-${width} h-${height} rounded bg-base-200`;

  return (
    <div className={`flex flex-col gap-${gap}`}>
      {Array.from({ length: count }).map((_, index) => (
        <Skeleton key={index} className={skeletonClass} />
      ))}
    </div>
  );
};

export default Loader;
