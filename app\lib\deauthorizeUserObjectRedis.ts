import { IUserSessionData } from "@/app/interfaces/IUserSessionData";
import {
  setHashInRedis,
  getAllHashFromRedis,
  getSessionIdsForCandidate,
  getHashFromRedis,
} from "@/app/lib/redisHelper";

export async function deauthorizeUserObjectRedis(
  candidateNumber: string,
  specificSessionId?: string
): Promise<string> {
  try {
    // If a specific session ID is provided, only deauthorize that session
    if (specificSessionId) {
      try {
        const key = `candidate:${candidateNumber}:${specificSessionId}`;
        const isAuthorized = await getHashFromRedis(key, "isAuthorized");

        if (isAuthorized === "true") {
          await setHashInRedis(key, "isAuthorized", "false");
          return `${candidateNumber} sesjon ${specificSessionId} ble avautorisert`;
        } else {
          return `Sesjon ${specificSessionId} var ikke autorisert`;
        }
      } catch (error) {
        console.error(`Feil ved oppdatering av spesifikk sesjon: ${error}`);
        throw new Error("En feil oppstod ved oppdatering av spesifikk sesjon");
      }
    }

    // Original behavior: deauthorize all sessions
    const sessionIds = await getSessionIdsForCandidate(candidateNumber);

    if (!sessionIds || sessionIds.length === 0) {
      return "Ingen data funnet";
    }

    const updatePromises = sessionIds.map(async (sessionId) => {
      try {
        const key = `candidate:${candidateNumber}:${sessionId}`;
        const isAuthorized = await getHashFromRedis(key, "isAuthorized");

        if (isAuthorized === "true") {
          await setHashInRedis(key, "isAuthorized", "false");
          return key;
        }

        return null;
      } catch (error) {
        console.error(`Feil ved oppdatering av Redis-data: ${error}`);
        return null;
      }
    });

    const results = await Promise.all(updatePromises);
    const successfulUpdates = results.filter(Boolean).length;

    if (successfulUpdates === 0) {
      return "Ingen oppdateringer ble utført";
    }

    return `${candidateNumber} ble avautorisert (${successfulUpdates} sesjonsobjekter)`;
  } catch (error) {
    throw new Error("En feil oppstod ved oppdatering av autorisasjonsstatus");
  }
}
