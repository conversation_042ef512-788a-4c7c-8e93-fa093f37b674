import { MigrationInterface, QueryRunner } from "typeorm";

export class ConvertTimestampToVarchar1749205961400 implements MigrationInterface {
    
    public async up(queryRunner: QueryRunner): Promise<void> {
        // Convert Timestamp to VARCHAR(50)
        await queryRunner.query(`
            ALTER TABLE AuditLog 
            ALTER COLUMN Timestamp VARCHAR(50)
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Convert Timestamp back to DATETIME2
        await queryRunner.query(`
            ALTER TABLE AuditLog 
            ALTER COLUMN Timestamp DATETIME2
        `);
    }
}
