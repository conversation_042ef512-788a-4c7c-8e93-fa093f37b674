import { MigrationInterface, QueryRunner } from "typeorm";

export class AddOperationTableAndNormalizeAuditlog1749206000000 implements MigrationInterface {
    name = 'AddOperationTableAndNormalizeAuditlog1749206000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create Operation table
        await queryRunner.query(`CREATE TABLE "Operation" ("OperationID" int NOT NULL, "Operasjonstype" varchar(255) NOT NULL, "BeskrivelseMal" text NOT NULL, CONSTRAINT "PK_Operation" PRIMARY KEY ("OperationID"))`);
        
        // Insert operation data
        await queryRunner.query(`
            INSERT INTO "Operation" ("OperationID", "Operasjonstype", "BeskrivelseMal") VALUES
            (1, 'Innlogging', 'Kandidaten logget inn - ny sesjon startet. Kandidaten kom til {side}'),
            (2, 'Autorisering', 'PGS ba automatisk om tilgang for kandidaten.'),
            (3, 'Autorisering', 'Kandidaten ble autorisert med dagspassord.'),
            (4, 'Autorisering', 'Kandidaten fikk tilgang fra kandidatmonitoren. Kandidaten kom til {side}'),
            (5, 'Sjekking', 'PGS satte kandidatstatus automatisk til Venter på eksamensstart.'),
            (6, 'Sjekking', 'PGS satte kandidatstatus automatisk til Venter på eksamensstart for del 2.'),
            (7, 'Sjekking', 'PGS satte status automatisk til Startet.'),
            (8, 'Sjekking', 'PGS satte status automatisk til Startet for del 2.'),
            (9, 'Nedlasting', 'Kandidaten lastet ned oppgavefilen.'),
            (10, 'Oppgave', 'Kandidaten åpnet forhåndsvisningen for oppgavefilen.'),
            (11, 'Nedlasting', 'Kandidaten lastet ned forberedelesesfilen.'),
            (12, 'Oppgave', 'Kandidaten åpnet forhåndsvisningen for forberedelsesfilen.'),
            (13, 'Opplasting', 'Kandidaten lastet opp filen.'),
            (14, 'Sjekking', 'PGS satte kandidatstatus automatisk til "Laster opp".'),
            (15, 'Opplasting', 'Kandidaten lastet opp filen.'),
            (16, 'Sletting', 'Kandidaten slettet filen.'),
            (17, 'Nedlasting', 'Kandidaten sjekket filen (lastet ned filen).'),
            (18, 'Nedlasting', 'Kandidaten lastet ned filen.'),
            (19, 'Levering', 'Kandidaten leverte digitalt. Totalt {antallFiler} filer er levert.'),
            (20, 'Levering', 'PGS satte status automatisk til Levert digitalt. Digital tilgang er stengt.'),
            (21, 'Levering', 'Kandidaten leverte digitalt del 2. Totalt {antallFiler} filer er levert.'),
            (22, 'Levering', 'PGS satte status automatisk til Levert digitalt for del 2. Digital tilgang er stengt.'),
            (23, 'Utlogging', 'Kandidaten logget ut. Innloggingssesjonen ble avsluttet'),
            (24, 'Innlogging', 'Kandidaten logget inn - ny sesjon startet. Digital tilgang er stengt da oppmøtestatus er ikke-dokumentert fravær.'),
            (25, 'Nedlasting', 'Kandidaten lastet ned kvittering.')
        `);

        // Add new columns to AuditLog
        await queryRunner.query(`ALTER TABLE "AuditLog" ADD "OperationID" int NOT NULL DEFAULT 1`);
        await queryRunner.query(`ALTER TABLE "AuditLog" ADD "Parameters" nvarchar(MAX)`);

        // Since OperasjonBeskrivelse was removed in a previous migration, 
        // we'll set a default OperationID for existing records
        // New records will be inserted with proper OperationID values by the application

        // Add foreign key constraint
        await queryRunner.query(`ALTER TABLE "AuditLog" ADD CONSTRAINT "FK_AuditLog_Operation" FOREIGN KEY ("OperationID") REFERENCES "Operation"("OperationID")`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop foreign key constraint
        await queryRunner.query(`ALTER TABLE "AuditLog" DROP CONSTRAINT "FK_AuditLog_Operation"`);
        
        // Drop new columns
        await queryRunner.query(`ALTER TABLE "AuditLog" DROP COLUMN "Parameters"`);
        await queryRunner.query(`ALTER TABLE "AuditLog" DROP COLUMN "OperationID"`);
        
        // Drop Operation table
        await queryRunner.query(`DROP TABLE "Operation"`);
    }
}
