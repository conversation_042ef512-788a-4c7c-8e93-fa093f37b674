import { getAppInsightsServer } from "@/app/lib/appInsightsServer";
import { getDeliveredFiles } from "@/app/lib/getDeliveredFiles";
import { getUserSessionData } from "@/app/lib/getUserSessionData";
import { getHashFromRedis, getMultipleHashFields } from "@/app/lib/redisHelper";
import { NextResponse } from "next/server";

export const dynamic = "force-dynamic";

const telemetryClient = getAppInsightsServer();

export async function GET() {
  try {
    const userSessionData = await getUserSessionData();
    if (!userSessionData?.userSessionId) {
      console.warn("Unauthorized access attempt");
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    // Only get the isAuthorized field from Redis hash
    const userKey = `candidate:${userSessionData.candidateNumber}:${userSessionData.userSessionId}`;
    const isAuthorizedValue = await getHash<PERSON>romRedis(userKey, "isAuthorized");

    if (!isAuthorizedValue) {
      console.warn(
        `Unauthorized access attempt for user: ${userSessionData.userSessionId}`
      );
      return NextResponse.json(
        { message: "User not authorized" },
        { status: 403 }
      );
    }

    // Get both candidateNumber and candidateGroupCode in a single Redis operation
    const [candidateNumber, candidateGroupCode] = await getMultipleHashFields(
      userKey,
      ["candidateNumber", "candidateGroupCode"]
    );

    if (!candidateNumber || !candidateGroupCode) {
      console.error(
        `Required user info not found for GUID: ${userSessionData.userSessionId}`
      );
      return NextResponse.json(
        { message: "Required user info not found" },
        { status: 404 }
      );
    }

    const blobs = await getDeliveredFiles(candidateNumber, candidateGroupCode);

    if (!blobs || blobs.length === 0) {
      return NextResponse.json({ blobs: [] });
    }

    return NextResponse.json({
      blobs: blobs,
    });
  } catch (error) {
    console.error("Error in getUserBlobs", error);

    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        Message: "Error in getUserBlobs",
      },
    });

    if (error instanceof Error) {
      // Log the full error details for debugging
      console.error(`Error details: ${error.message}\n${error.stack}`);
      // Determine the appropriate status code based on the error
      let statusCode = 500;
      if (error.message.includes("not found")) {
        statusCode = 404;
      } else if (error.message.includes("unauthorized")) {
        statusCode = 403;
      }
      return NextResponse.json(
        {
          error: "En feil oppstod under henting av filer",
          details: error.message,
        },
        { status: statusCode }
      );
    }
    return NextResponse.json(
      { error: "En uventet feil oppstod" },
      { status: 500 }
    );
  }
}
