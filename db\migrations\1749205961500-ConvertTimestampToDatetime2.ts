import { MigrationInterface, QueryRunner } from "typeorm";

export class ConvertTimestampToDatetime21749205961500 implements MigrationInterface {
    
    public async up(queryRunner: QueryRunner): Promise<void> {
        // First, create a temporary column with datetime2
        await queryRunner.query(`
            ALTER TABLE AuditLog 
            ADD TimestampTemp DATETIME2 NULL
        `);

        // Update the temporary column by converting the varchar data
        await queryRunner.query(`
            UPDATE AuditLog 
            SET TimestampTemp = TRY_CONVERT(DATETIME2, Timestamp)
        `);

        // Drop the old varchar column
        await queryRunner.query(`
            ALTER TABLE AuditLog 
            DROP COLUMN Timestamp
        `);

        // Rename the temporary column to Timestamp
        await queryRunner.query(`
            EXEC sp_rename 'AuditLog.TimestampTemp', 'Timestamp', 'COLUMN'
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Convert Timestamp back to VARCHAR(50)
        await queryRunner.query(`
            ALTER TABLE AuditLog 
            ALTER COLUMN Timestamp VARCHAR(50)
        `);
    }
}
