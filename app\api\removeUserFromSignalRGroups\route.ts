import { generateSignalRAccessToken } from "@/app/lib/generateSignalRToken";
import { getUserSessionData } from "@/app/lib/getUserSessionData";
import { NextResponse } from "next/server";

const connectionString = process.env.CUSTOMCONNSTR_AZURE_SIGNALR_CONNECTION_STRING || "";
const hubName = process.env.AZURE_SIGNALR_HUB_NAME || "pgshub";
const endpoint = connectionString?.match(/Endpoint=(.*?);/)?.[1] ?? "";
const redisKey = "PGS:Signalr:Messages";

export async function DELETE(request: Request) {
  const userSessionData = await getUserSessionData();

  const url = `${endpoint}/api/v1/hubs/${hubName}/users/${userSessionData.candidateNumber}/groups`;

  try {
    const response = await fetch(url, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${generateSignalRAccessToken({
          audience: url,
          lifetime: 60,
        })}`,
      },
    });

    return NextResponse.json(
      { message: "User removed from signalr groups" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error removing user from signalr groups:", error);

    return NextResponse.json(
      { message: "Error removing user from signalr groups" },
      { status: 500 }
    );
  }
}
