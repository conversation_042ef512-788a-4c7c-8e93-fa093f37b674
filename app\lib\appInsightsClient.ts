"use client";

import { ApplicationInsights } from "@microsoft/applicationinsights-web";

let appInsights: ApplicationInsights | null = null;
let connectionString: string | null = null;

export async function initAppInsights(): Promise<ApplicationInsights | null> {
  if (!connectionString) {
    try {
      const response = await fetch("/api/getappinsightsconnection");
      if (!response.ok) {
        throw new Error(
          `Failed to fetch connection string: ${response.status}`
        );
      }
      const data = await response.json();
      connectionString = data.connectionString;
    } catch (error) {
      console.warn("Failed to fetch connection string:", error);
      return null;
    }
  }

  if (!connectionString) {
    console.warn("Application Insights connection string is missing.");
    return null;
  }

  if (!appInsights) {
    try {
      appInsights = new ApplicationInsights({
        config: {
          connectionString: connectionString,
          enableAutoRouteTracking: true,
          disableAjaxTracking: false,
          disableFetchTracking: false,
          enableCorsCorrelation: true,
          enableRequestHeaderTracking: false,
          enableResponseHeaderTracking: false,
          samplingPercentage: 100,
        },
      });

      appInsights.loadAppInsights();
    } catch (error) {
      console.warn("Application Insights initialization failed:", error);

      return null;
    }
  }

  return appInsights;
}

export async function getAppInsights(): Promise<ApplicationInsights | null> {
  if (!appInsights) {
    return await initAppInsights();
  }
  return appInsights;
}

export async function logEvent(
  name: string,
  properties?: { [key: string]: any }
): Promise<void> {
  const ai = await getAppInsights();
  if (ai) {
    ai.trackEvent({ name, properties });
  }
}

export async function logPageView(
  name?: string,
  uri?: string,
  properties?: { [key: string]: any }
): Promise<void> {
  const ai = await getAppInsights();
  if (ai) {
    ai.trackPageView({
      name: name || document.title,
      uri: uri || window.location.pathname,
      properties,
    });
  }
}

export async function logMetric(
  name: string,
  average: number,
  properties?: { [key: string]: any }
): Promise<void> {
  const ai = await getAppInsights();
  if (ai) {
    ai.trackMetric({ name, average, properties });
  }
}

export async function logException(
  error: Error,
  properties?: { [key: string]: any }
): Promise<void> {
  const ai = await getAppInsights();
  if (ai) {
    ai.trackException({ exception: error, properties });
  }
}

export async function clearUserContext(): Promise<void> {
  const ai = await getAppInsights();
  if (ai) {
    ai.clearAuthenticatedUserContext();
  }
}
