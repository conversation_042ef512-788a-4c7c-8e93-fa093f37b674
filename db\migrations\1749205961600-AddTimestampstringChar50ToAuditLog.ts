import { MigrationInterface, QueryRunner } from "typeorm";

export class AddTimestampstringChar50ToAuditLog1749205961600 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        // Check if the column already exists (with any casing)
        const result = await queryRunner.query(`
            SELECT COUNT(*) as count
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'AuditLog'
            AND (COLUMN_NAME = 'Timestampstring' OR COLUMN_NAME = 'TimestampString')
        `);
        
        if (result[0].count === 0) {
            await queryRunner.query(`
                ALTER TABLE AuditLog
                ADD Timestampstring CHAR(50)
            `);
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Check if the column exists before dropping it
        const result = await queryRunner.query(`
            SELECT COUNT(*) as count
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'AuditLog'
            AND COLUMN_NAME = 'Timestampstring'
        `);
        
        if (result[0].count > 0) {
            await queryRunner.query(`
                ALTER TABLE AuditLog
                DROP COLUMN Timestampstring
            `);
        }
    }
}
