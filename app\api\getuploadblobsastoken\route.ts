import { CandidateStatusEnum } from "@/app/enums/CandidateStatusEnum";
import {
  generateSasTokenQueryString,
  SasTokenConfig,
  BlobServiceManager,
} from "@/app/lib/blobHelper"; // Added BlobServiceManager import
import { getStatusInfoFromPgsa } from "@/app/lib/getStatusInfoFromPgsa";
import { getUserSessionData } from "@/app/lib/getUserSessionData";
import { getHashFromRedis } from "@/app/lib/redisHelper";
import { NextResponse } from "next/server";
import { v4 as uuidv4 } from "uuid";
import { getAppInsightsServer } from "@/app/lib/appInsightsServer";
import { IUserSessionData } from "@/app/interfaces/IUserSessionData";
import { IStatusInfo } from "@/app/interfaces/IStatusInfo";

// Constants
const COMPONENT_NAME = "pgsxDocumentsApi";
const CONTAINER_NAME = "pgsx-documents";
const PERMISSION = "wtr" as const;

// Token configuration (base expiry)
const TOKEN_CONFIG: SasTokenConfig = {
  startOffsetMinutes: 2,
  expiryMinutes: 10,
  cacheDurationMinutes: 8,
} as const;

// Interfaces
interface ApiResponse {
  fileguid?: string;
  sastoken?: string;
  message?: string;
  error?: string;
}

const telemetryClient = getAppInsightsServer();

function isUserAuthorized(
  isAuthorized: string | null,
  pgsaInfo?: IStatusInfo
): boolean {
  return (
    isAuthorized === "true" &&
    (pgsaInfo?.Status === CandidateStatusEnum.LastetOpp ||
      pgsaInfo?.Status === CandidateStatusEnum.InnloggetAutentisert)
  );
}

function getTelemetryProperties(
  action: string,
  userSessionData?: IUserSessionData,
  pgsaInfo?: IStatusInfo
) {
  return {
    component: COMPONENT_NAME,
    action,
    userSessionData: userSessionData,
    pgsaInfo: pgsaInfo,
  };
}

export const dynamic = "force-dynamic";

export async function GET() {
  let userSessionData: IUserSessionData | null = null;

  try {
    userSessionData = await getUserSessionData();
    if (!userSessionData?.userSessionId) {
      telemetryClient?.trackException({
        exception: new Error("Unauthorized - Missing user Guid"),
        properties: getTelemetryProperties("Unauthorized", userSessionData),
      });

      return NextResponse.json(
        { message: "Unauthorized - Missing user session" },
        { status: 401 }
      );
    }

    const userKey = `candidate:${userSessionData.candidateNumber}:${userSessionData.userSessionId}`;

    const [isAuthorized, pgsaInfo] = await Promise.all([
      getHashFromRedis(userKey, "isAuthorized"),
      getStatusInfoFromPgsa(userSessionData.userId),
    ]);

    if (!isUserAuthorized(isAuthorized, pgsaInfo ?? undefined)) {
      telemetryClient?.trackException({
        exception: new Error(
          "NotAuthorized - User not authorized to get blob-token"
        ),
        properties: {
          ...getTelemetryProperties(
            "NotAuthorized",
            userSessionData,
            pgsaInfo ?? undefined
          ),
        },
      });

      return NextResponse.json(
        { message: "User not authorized to get blob-token" },
        { status: 403 }
      );
    }

    // Generate a unique blob name for this upload request
    const blobName = uuidv4();

    // Get the cached container SAS query string from blobHelper
    const sasQueryString = await generateSasTokenQueryString(
      CONTAINER_NAME,
      "", // Empty blobName to get container SAS query string
      PERMISSION,
      TOKEN_CONFIG,
      true // Enable caching in blobHelper
    );

    // Construct the full URL with the unique blob name and the container SAS query string
    const config = BlobServiceManager.getConfig(); // Need to import BlobServiceManager
    const fullSasUrl = `${config.pgsUrl}/${CONTAINER_NAME}/${blobName}?${sasQueryString}`;

    const response: ApiResponse = {
      fileguid: blobName, // Return the generated blob name
      sastoken: fullSasUrl, // Return the full URL
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error in pgsxDocumentsApi:", error);

    telemetryClient?.trackException({
      exception: error as Error,
      properties: getTelemetryProperties("Error", userSessionData ?? undefined),
    });

    const response: ApiResponse = {
      error: "Internal Server Error",
    };

    return NextResponse.json(response, { status: 500 });
  }
}
