import { getStatusInfoFromPgsa } from "@/app/lib/getStatusInfoFromPgsa";
import { getUserSessionData } from "@/app/lib/getUserSessionData";
import { deleteHashFieldFromRedis } from "@/app/lib/redisHelper";
import { sendSignalRStatusRequest } from "@/app/lib/sendSignalRStatusRequest";
import { NextResponse } from "next/server";

//added this because of a dynamic error during build
//https://stackoverflow.com/questions/76285120/error-dynamic-server-usage-headers-on-next-13-4
export const dynamic = "force-dynamic";

export async function GET(request: Request) {
  try {
    const userSessionData = await getUserSessionData();

    if (!userSessionData?.userSessionId)
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });

    const userInfoFromPgsa = await getStatusInfoFromPgsa(
      userSessionData.userId
    );

    const { searchParams } = new URL(request.url);
    const idToken = searchParams.get("idtoken") ?? null;

    if (userInfoFromPgsa?.SchoolId && userInfoFromPgsa?.CandidateNumber) {
      try {
        await sendSignalRStatusRequest(
          userInfoFromPgsa.SchoolId,
          userInfoFromPgsa.CandidateNumber,
          false,
          userSessionData.userSessionId
        );

        // Delete specific candidate's request from Redis
        const key = `AccessRequest:${userInfoFromPgsa?.SchoolId}`;
        await deleteHashFieldFromRedis(key, userInfoFromPgsa.CandidateNumber);
      } catch (error) {
        console.log("Error", error);
      }
    } else {
      return NextResponse.json(
        { message: "Invalid user information" },
        { status: 400 }
      );
    }

    if (idToken) {
      const redirectUrl =
        process.env.NEXTAUTH_UIDP_URL +
        "/connect/endsession?id_token_hint=" +
        idToken;

      const response = { url: redirectUrl };

      return NextResponse.json(response);
    }

    const redirectUrl = process.env.NEXTAUTH_UIDP_URL + "/connect/endsession";

    const response = { url: redirectUrl };

    return NextResponse.json(response);
  } catch (err) {
    console.log("Error", err);
  }
}
