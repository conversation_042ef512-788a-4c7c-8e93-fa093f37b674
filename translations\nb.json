{"Navigation": {"ChangeLanguage": "Bytt til nynorsk", "ChangeLanguage2": "Vis siden på nynorsk", "Logout": "Logg ut"}, "Autorisering": {"PictureStandingPerson": "<PERSON><PERSON> av st<PERSON><PERSON>e person", "WelcomeMessage": "<PERSON><PERSON>, {displayName}", "NeedAccessToExam": "Du trenger tilgang til eksamen", "AlreadyAccess": "Det er eksamensvaktene som gir tilgang. Du blir automatisk sendt videre når du får tilgang.", "GotAccess": "Be om tilgang", "Send": "Send", "ExamInviligatorWritePassword": "Eksamensvakten kan skrive inn dagspassord", "WriteDayPassword": "Skriv inn dagspassord", "NoAccessError": "Du har enda ikke fått tilgang", "WrongPassword": "Passordet er feil", "FieldEmpty": "Feltet kan ikke være tom", "Error": "Uventet feil oppstått", "ControlCode": "Kontrollkode", "MessageSent": "Eksamensvaktene er varslet.", "DontRefresh": "Vennligst ikke oppdater siden før en eksamensvakt har gitt deg tilgang.", "AccessDenied": "Tilgang a<PERSON>", "AccessDeniedMessage": "<PERSON> forespørsel om tilgang til eksamen er avslått. Ta kontakt med en eksamensvakt for mer informasjon.", "SendNewRequest": "Send ny forespørsel"}, "Kandidatinfo": {"CandidateNumber": "Kandidatnummer", "SubjectCode": "Fagkode"}, "Klar": {"WelcomeMessage": "<PERSON><PERSON>, {displayName}", "ReadyExamNotStarted": "Flott! Nå er du helt klar, men eksamen har ikke startet.", "ReadyExamTwoPartsNotStarted": "Flott! Nå er du helt klar, men del 2 har ikke startet.", "GoToExam": "Gå til eksamen", "PictureSittingPerson": "<PERSON><PERSON> av sittende person", "ExamStartsIn": "Eksamen starter om:", "ExamStartsInTwoParts": "Del 2 starter om:", "SendForward": "Vi sender deg automatisk videre. Lykke til!", "Hours": "timer", "Minutes": "minutter", "Seconds": "<PERSON><PERSON>nder", "Hour": "time", "Minute": "minutt", "Second": "sekund", "day": "dag", "days": "dager", "feil": "Noe feil har op<PERSON><PERSON><PERSON>", "UnderOneMinute": "under ett minutt", "Exam": "Eksamen", "ExamTime": "Eksamenstider"}, "Eksamensoppgave": {"WelcomeToExam": "Velkommen til eksamen, ", "DeliverExamIn": "Lever eksamen i ", "ExamOpenToDeliver": "Eksamen er åpen for levering", "DeliverExam": "Last opp be<PERSON><PERSON><PERSON><PERSON>", "ExaminationPaper": "Eksamensoppgave", "PreparationPaper": "Forberedelsesoppgaver", "ErrorLoadingExercise": "Feil oppstått. Klarte ikke å hente eksamensoppgaven", "Next": "Neste", "Previous": "<PERSON><PERSON><PERSON>", "Malform": "Målform", "DownloadAssignment": "Last ned oppgave", "PreviewAssignment": "Forhåndsvis oppgave", "PreviewAssignmentNotAvailable": "Forhåndsvisning ikke tilgjengelig for denne filtypen", "PreviewFailed": "Kunne ikke hente oppgaven"}, "Levering": {"HeadingMessage": "Her laster du opp filene dine", "Delete": "<PERSON><PERSON>", "UploadDate": "Opplastingsdato", "Size": "<PERSON><PERSON><PERSON><PERSON>", "DownLoad": "Last ned", "ErrorLoadFiles": "Feil oppstått. Klarte ikke å hente filene", "UploadFailed": "Opplasting feilet", "DownloadFailed": "Nedlasting feilet", "DeleteFailed": "Sletting feilet", "NotUniqueFilname": "Filnavnet er ikke unikt", "FilesizeTooLarge": "Filen er for stor", "FileNotAllowed": "Filtypen er ikke tillatt", "Checked": "Sjekket", "NotChecked": "Ikke sjekket", "CheckYourFiles": "<PERSON><PERSON><PERSON><PERSON> filene dine", "DragFilesHere": "Dra filene hit (maks 20 filer)....", "UploadInProgress": " Opplasting pågår...", "UploadLoadYourFiles": "Last opp filene dine", "Or": "eller", "UploadFileLabel": "Trykk her for å laste opp filene dine", "CheckFilesMessage": "<PERSON><PERSON>k<PERSON> at du har lastet opp korrekte filer ved å laste dem ned. Hvis du skal gjøre endringer før du leverer må du slette filen, gjøre endringene dine og så laste opp filen på nytt.  Hvis skolen har levert filer på dine vegne, vil du ikke kunne se disse her, men du vil kunne se og laste ned alle leverte filer på <link>kandidat.udir.no</link> i morgen.", "DeliverYourFiles": "Lever filene dine", "DeliverFiles": "Lever filene", "Tilbake": "Tilbake", "NoFilesDeliver": "Du har ingen filer å levere", "DownLoadCheckDeliver": "Du må laste ned og sjekke alle filene før du kan levere de", "DuplicateNameError": "må slettes på grunn av duplikat filnavn", "FilesizeError": "må slettes på grunn av for stor stø<PERSON>se", "FiletypeNotAllowed": "må slettes på grunn av ugyldig filtype", "FileUploadError": "må slettes på grunn av feil ved opplasting", "ConfirmDeleteHeading": "Bekreft sletting", "ConfirmDeleteMessage": "<PERSON>r du sikker på at du vil slette filen", "ConfirmDeleteConfirmBtn": "<PERSON><PERSON> filen", "Cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ConfirmationFileDelivery": "Gi siste bekreftelse før levering", "ConfirmDelivery": "Bekreft levering", "IConfirm": "<PERSON><PERSON> bek<PERSON>fter at", "FilesAreCorrect": "Mine filer er korrekte", "CannotEditAnswer": "Jeg vet at jeg ikke kan endre min besvarelse etter levering", "EmptyFile": "Filen er tom", "TooManyFilesTitle": "Du har lastet opp for mange filer", "TooManyFilesMessage": "Du kan maksimalt laste opp 20 filer. Vennligst slett en eller flere filer før du laster opp flere."}, "Kvittering": {"ReceiptDeliveredExam": "Kvittering for levert eksamen", "DoNotClosePage": "Ikke lukk siden eller logg ut før du har fått tillatelse fra en eksamensvakt.", "CandidateNumber": "Kandidatnummer", "Fag": "Fag", "Status": "Status", "Submitted": "<PERSON><PERSON>", "ExamAnswersAvailable": "Om ett døgn blir besvar<PERSON>en din tilgjengelig på kandidat.udir.no", "DeliveredFiles": "Dine leverte filer", "ErrorGettingFiles": "<PERSON><PERSON>, klarte ikke å hente filen(e)", "DownloadReciept": "Last ned kvi<PERSON>en", "MessageDeliveredOnPaper": "Kandidaten har levert på papir.", "Size": "<PERSON><PERSON><PERSON><PERSON>"}, "Fravaer": {"AbsenceHeading": "Du er registrert med fravær", "AbsenceRegistered": "Du er registrert med fravær og har derfor ikke tilgang til eksamen. Dersom dette er feil, ta kontakt med en eksamensvakt for hjelp.", "TryAgain": "Når en eksamensvakt har fjernet fraværet, kan du logge ut og deretter logge inn igjen"}, "Feil": {"PageNotFound": "Hmm, denne siden fant vi ikke...", "PageNotFoundText": "Siden du prøvde å nå finnes ikke", "GoToHomePage": "<PERSON><PERSON> til forsiden", "ErrorMessage": "Feilmelding", "HTTPGeneralError": "Beklager noe gikk galt. Prøv igjen senere.", "TryAgain": "<PERSON><PERSON><PERSON><PERSON> ig<PERSON>n", "ErrorCode": "Feilkode", "ErrorType": "FeilType", "Time": "Klokkeslett", "Page": "Side", "Service": "Tjeneste"}, "IkkeTilgang": {"Header": "Du har ikke digital tilgang til eksamen", "Message": "Du har mistet digital tilgang til eksamen. Dersom dette er feil, ta kontakt med en eksamensvakt for hjelp."}}