"use client";

import React, { useEffect, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { signIn, useSession } from "next-auth/react";
import { AiOutlineLoading3Quarters } from "react-icons/ai";
import { RouteEnum } from "@/app/enums/RouteEnum";

function SigninContent() {
  const router = useRouter();
  const { status } = useSession();
  const searchParams = useSearchParams();

  useEffect(() => {
    async function signInWithProvider() {
      if (status === "unauthenticated") {
        await signIn("UIDP");
      } else if (status === "authenticated") {
        const state = searchParams.get("state");
        if (state) {
          router.push(decodeURIComponent(state));
        } else {
          router.push(RouteEnum.Hjem);
        }
      }
    }
    signInWithProvider();
  }, [status, router, searchParams]);

  return (
    <div
      style={{
        padding: "4rem",
      }}
    >
      <style>
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
          .spinner {
            animation: spin 1s linear infinite;
          }
        `}
      </style>
      <AiOutlineLoading3Quarters
        className="spinner"
        style={{ fontSize: "2rem" }}
      />
    </div>
  );
}

export default function Signin() {
  return (
    <Suspense fallback={
      <div style={{ padding: "4rem" }}>
        <AiOutlineLoading3Quarters
          className="spinner"
          style={{ fontSize: "2rem" }}
        />
      </div>
    }>
      <SigninContent />
    </Suspense>
  );
}
