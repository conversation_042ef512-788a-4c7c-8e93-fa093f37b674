"use client";

import * as signalR from "@microsoft/signalr";
import { HubConnection, HubConnectionState } from "@microsoft/signalr";
import { logException } from "./appInsightsClient";

// --- Konfigurasjon ---
const SIGNALR_HUB_URL = "/api";
const CONNECTION_TIMEOUT_MS = 30000; // 30 sekunder timeout
const RECONNECT_INTERVALS = [0, 2000, 10000, 30000]; // Forsøk på nytt etter 0s, 2s, 10s, 30s

// Circuit Breaker konfigurasjon
const CIRCUIT_BREAKER_FAILURE_THRESHOLD = 5;
const CIRCUIT_BREAKER_TIMEOUT = 60000; // 1 minutt
const CIRCUIT_BREAKER_SUCCESS_THRESHOLD = 2;

// --- State ---
let connectionPromise: Promise<HubConnection> | null = null;
let connectionInstance: HubConnection | null = null;
let isCleaningUp: boolean = false;

// Circuit Breaker state
let circuitState: "CLOSED" | "OPEN" | "HALF_OPEN" = "CLOSED";
let failureCount: number = 0;
let successCount: number = 0;
let lastFailureTime: number = 0;
let nextAttemptTime: number = 0;

// Circuit Breaker functions
function canAttemptConnection(): boolean {
  const now = Date.now();

  switch (circuitState) {
    case "CLOSED":
      return true;

    case "OPEN":
      if (now >= nextAttemptTime) {
        circuitState = "HALF_OPEN";
        successCount = 0;
        console.log("Circuit breaker moving to HALF_OPEN state");
        return true;
      }
      console.log(
        `Circuit breaker OPEN. Next attempt in ${Math.round(
          (nextAttemptTime - now) / 1000
        )}s`
      );
      return false;

    case "HALF_OPEN":
      return true;

    default:
      return false;
  }
}

function recordConnectionSuccess(): void {
  failureCount = 0;

  if (circuitState === "HALF_OPEN") {
    successCount++;
    if (successCount >= CIRCUIT_BREAKER_SUCCESS_THRESHOLD) {
      circuitState = "CLOSED";
      console.log("Circuit breaker moving to CLOSED state");
    }
  }
}

function recordConnectionFailure(): void {
  failureCount++;
  lastFailureTime = Date.now();

  if (circuitState === "HALF_OPEN") {
    openCircuit();
  } else if (failureCount >= CIRCUIT_BREAKER_FAILURE_THRESHOLD) {
    openCircuit();
  }
}

function openCircuit(): void {
  circuitState = "OPEN";
  nextAttemptTime = Date.now() + CIRCUIT_BREAKER_TIMEOUT;
  console.log(
    `Circuit breaker OPEN. Next attempt at ${new Date(nextAttemptTime)}`
  );
}

// Forbedret cleanup function
async function safeCleanup(): Promise<void> {
  if (isCleaningUp) return;

  isCleaningUp = true;
  try {
    const instance = connectionInstance;
    if (instance) {
      // Remove all event listeners to prevent memory leaks
      instance.off("close");
      instance.off("reconnected");
      instance.off("reconnecting");

      if (instance.state !== HubConnectionState.Disconnected) {
        await instance.stop();
      }
    }
  } catch (error) {
    console.warn("Error during cleanup:", error);
  } finally {
    connectionPromise = null;
    connectionInstance = null;
    isCleaningUp = false;
  }
}

/**
 * Oppretter eller returnerer en eksisterende (og potensielt pågående)
 * SignalR-tilkobling som en Promise.
 * Implementerer circuit breaker pattern og forbedret error handling.
 *
 * @returns {Promise<HubConnection>} Et promise som løses med HubConnection-instansen når den er tilkoblet.
 * @throws Kaster feil hvis tilkoblingen mislykkes eller circuit breaker er åpen.
 */
export function getSignalRConnection(): Promise<HubConnection> {
  // Check circuit breaker first
  if (!canAttemptConnection()) {
    const timeUntilNext = Math.max(0, nextAttemptTime - Date.now());
    return Promise.reject(
      new Error(
        `Circuit breaker is OPEN. Next attempt in ${Math.round(
          timeUntilNext / 1000
        )}s`
      )
    );
  }

  if (!connectionPromise) {
    console.log(
      "Ingen eksisterende SignalR connection promise funnet. Oppretter ny."
    );

    connectionPromise = new Promise(async (resolve, reject) => {
      try {
        const newConnection = new signalR.HubConnectionBuilder()
          .withUrl(SIGNALR_HUB_URL)
          .withAutomaticReconnect(RECONNECT_INTERVALS)
          .configureLogging(signalR.LogLevel.Warning)
          .build();

        // Lagre referansen til dette spesifikke promise for onclose-handleren
        const promiseInstance = connectionPromise;

        // Viktig: Håndter 'close'-hendelsen for å rydde opp promise
        // hvis tilkoblingen lukkes permanent (enten manuelt eller etter at auto-reconnect gir opp).
        newConnection.onclose(async (error) => {
          console.warn(
            `SignalR connection closed. State: ${newConnection.state}. Error: ${error}`
          );

          // Nullstill slik at neste getSignalRConnection() starter en ny tilkobling.
          // Gjør dette kun hvis tilstanden faktisk er disconnected (ikke under reconnecting)
          if (newConnection.state === HubConnectionState.Disconnected) {
            // Sjekk om vi har samme promise fortsatt for å unngå race conditions ved stop/close
            if (connectionPromise === promiseInstance) {
              console.log("Nullstiller connectionPromise pga. onclose.");
              connectionPromise = null;
              connectionInstance = null;
            }
          }
        });

        // Håndter reconnected for logging eller UI-oppdateringer
        newConnection.onreconnected((connectionId) => {
          console.log(
            `SignalR connection reconnected successfully. ConnectionId: ${connectionId}`
          );
          // Her kan du potensielt varsle UI eller re-synkronisere tilstand
        });

        // Håndter reconnecting for logging eller UI-oppdateringer
        newConnection.onreconnecting((error) => {
          console.warn(
            `SignalR connection attempting to reconnect... Error: ${error}`
          );
          // Her kan du vise en melding til brukeren om at tilkoblingen er ustabil
        });

        // Timeout håndtering
        try {
          await Promise.race([
            newConnection.start(),
            new Promise<never>((_, rejectTimeout) => {
              setTimeout(
                () => rejectTimeout(new Error("SignalR connection timeout")),
                CONNECTION_TIMEOUT_MS
              );
            }),
          ]);
        } catch (timeoutError) {
          // Hvis dette er en timeout, stopp tilkoblingsforsøket før vi kaster feilen
          if (
            timeoutError instanceof Error &&
            timeoutError.message === "SignalR connection timeout"
          ) {
            try {
              await newConnection.stop();
            } catch (stopError) {
              console.warn(
                "Feil ved stopping av timed out connection:",
                stopError
              );
            }
          }
          throw timeoutError;
        }

        console.log(
          `SignalR connected successfully! State: ${newConnection.state}`
        );
        connectionInstance = newConnection; // Lagre instansen

        // Record successful connection for circuit breaker
        recordConnectionSuccess();

        resolve(newConnection);
      } catch (error) {
        console.error("Feil under oppstart av SignalR-tilkobling:", error);

        // Record failure for circuit breaker
        recordConnectionFailure();

        await logException(
          error instanceof Error
            ? error
            : new Error("Unknown error during SignalR start"),
          {
            properties: {
              component: "ClientSignalRService",
              method: "getSignalRConnection_PromiseExecutor",
              circuitState,
              failureCount,
            },
          }
        );

        // Use safe cleanup instead of direct nulling
        await safeCleanup();
        reject(error); // Send feilen videre
      }
    });
  } else {
    console.log("Returnerer eksisterende SignalR connection promise.");
  }

  // Returner alltid det (potensielt pågående) promise
  return connectionPromise;
}

/**
 * Stopper den aktive SignalR-tilkoblingen hvis den finnes.
 * Håndterer også tilfellet der tilkoblingen fortsatt er under oppstart.
 */
export async function stopSignalRConnection(): Promise<void> {
  const currentPromise = connectionPromise; // Ta en kopi i tilfelle den nullstilles
  if (!currentPromise) {
    console.log("Ingen aktiv SignalR-tilkobling eller promise å stoppe.");
    return;
  }

  console.log("Forsøker å stoppe SignalR-tilkobling...");
  try {
    // Viktig: Vent på at tilkoblings-promiset fullføres (enten OK eller feil)
    // før vi prøver å stoppe, i tilfelle stop kalles mens start pågår.
    const connection = await Promise.race([
      currentPromise,
      new Promise<never>((_, rejectTimeout) => {
        setTimeout(
          () =>
            rejectTimeout(new Error("Timeout venting på tilkobling før stop")),
          5000
        );
      }),
    ]);

    // Dobbeltsjekk instansen for sikkerhets skyld
    if (
      connectionInstance === connection &&
      connection.state !== HubConnectionState.Disconnected
    ) {
      await connection.stop();
      console.log("SignalR-tilkobling stoppet.");
    } else if (connection.state === HubConnectionState.Disconnected) {
      console.log("SignalR-tilkobling var allerede stoppet/disconnected.");
    } else {
      console.warn(
        "Kunne ikke stoppe SignalR-tilkoblingen, instans eller state mismatch?"
      );
    }
  } catch (error) {
    // Feil kan oppstå hvis 'start' feilet tidligere, eller under selve 'stop'.
    console.error("Feil under stopping av SignalR-tilkobling:", error);
    await logException(
      error instanceof Error
        ? error
        : new Error("Unknown error during SignalR stop"),
      {
        properties: {
          component: "ClientSignalRService",
          method: "stopSignalRConnection",
        },
      }
    );
    // Ikke kast feil her med mindre det er kritisk for applikasjonen
  } finally {
    // Uansett utfall, nullstill promise og instans for å tillate ny tilkobling senere.
    // Sjekk om det er *samme* promise vi startet med, for å unngå race conditions hvis
    // en ny tilkobling ble startet *etter* at vi kalte stop, men *før* vi nådde finally.
    if (connectionPromise === currentPromise) {
      console.log("Nullstiller connectionPromise etter stop-forsøk.");
      connectionPromise = null;
      connectionInstance = null;
    }
  }
}

/**
 * Hjelpefunksjon for å hente den aktive tilkoblingsinstansen
 * (hvis den er vellykket etablert).
 * MERK: Denne returnerer null hvis tilkoblingen ikke er etablert ennå eller har feilet/stoppet.
 * Bruk getSignalRConnection() for å garantere en tilkobling (eller feil).
 *
 * @returns {HubConnection | null} Den aktive HubConnection-instansen eller null.
 */
export function getActiveConnectionInstance(): HubConnection | null {
  // Sjekk både instansen og tilstanden for å være sikrere
  if (
    connectionInstance &&
    connectionInstance.state === HubConnectionState.Connected
  ) {
    return connectionInstance;
  }
  return null;
}

/**
 * Returnerer gjeldende circuit breaker status
 */
export function getCircuitBreakerStatus() {
  return {
    state: circuitState,
    failureCount,
    successCount,
    timeUntilNextAttempt:
      circuitState === "OPEN" ? Math.max(0, nextAttemptTime - Date.now()) : 0,
    canAttempt: canAttemptConnection(),
  };
}

/**
 * Manuell reset av circuit breaker (for testing eller admin-funksjoner)
 */
export function resetCircuitBreaker(): void {
  circuitState = "CLOSED";
  failureCount = 0;
  successCount = 0;
  lastFailureTime = 0;
  nextAttemptTime = 0;
  console.log("Circuit breaker manually reset to CLOSED state");
}
