import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateLeveringToInnlevering1749206700000 implements MigrationInterface {
    name = 'UpdateLeveringToInnlevering1749206700000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Change Levering to Innlevering for specific operations
        await queryRunner.query(`
            UPDATE "Operation"
            SET "Operasjonstype" = 'Innlevering'
            WHERE "OperationID" IN (120, 121, 122, 128, 129, 130);
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Revert Innlevering back to Levering
        await queryRunner.query(`
            UPDATE "Operation"
            SET "Operasjonstype" = 'Levering'
            WHERE "OperationID" IN (120, 121, 122, 128, 129, 130);
        `);
    }
}
