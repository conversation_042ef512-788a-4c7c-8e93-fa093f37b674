import "reflect-metadata";
import { DataSource } from "typeorm";

const AppDataSource = new DataSource({
  type: "mssql", // SQL Server
  host: process.env.DATABASE_HOST,
  port: 1433,
  username: process.env.DATABASE_USERNAME,
  password: process.env.DATABASE_PASSWORD,
  database: process.env.DATABASE_NAME,
  synchronize: false,
  logging: process.env.PGS_ENVIRONMENT !== "production",
  entities: [],
  migrations: ["db/migrations/*.ts"],
  migrationsTableName: "migrations",
  subscribers: [],  
  options: {
    encrypt: true,
    enableArithAbort: true,
    trustServerCertificate: false,
  },
  extra: {
    validateConnection: false,
    trustServerCertificate: false,
  },
  connectionTimeout: 30000,
  requestTimeout: 30000,
  migrationsRun: false,
  migrationsTransactionMode: "all",
});

export default AppDataSource;
