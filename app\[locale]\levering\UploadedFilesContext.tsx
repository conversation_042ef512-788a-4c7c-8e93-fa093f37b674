"use client";

import React, {
  createContext,
  useState,
  useContext,
  useCallback,
  useEffect,
} from "react";
import { BlobClient } from "@azure/storage-blob";
import { getUploadBlobSasToken } from "@/app/lib/getUploadBlobSasToken";
import { getBlobSasToken } from "@/app/lib/getBlobSasToken";
import { getBlob, getBlobIos } from "@/app/lib/getBlob";
import { FileType, IDeliveredFile } from "@/app/interfaces/IUploadFile";
import { logEvent, logException } from "@/app/lib/appInsightsClient";
import { TestPartsEnum } from "@/app/enums/TestParts";
import { logActivity } from "@/app/lib/logActivity";
import { OperationEnum } from "@/app/enums/OperationEnum";

// Constants
const BESVARELSE_CONTAINER_NAME =
  process.env.NEXT_PUBLIC_BESVARELSE_CONTAINER_NAME || "pgsx-documents";

// Interfaces
interface UploadedFilesContextType {
  uploadedFiles: IDeliveredFile[];
  fetchBlobsError: boolean;
  loadingBlobs: boolean;
  isUploading: boolean;
  addFile: (
    file: IDeliveredFile,
    candidateNumber: string,
    examGroupCode: string
  ) => Promise<void>;
  handleDownloadBlob: (
    blobGuid: string,
    fileName: string,
    testPartId: number,
    onProgress?: (progress: number, completed: boolean) => void
  ) => Promise<void>;
  handleDeleteFile: (file: FileType) => Promise<void>;
  downloadProgress: number | null;
  isDownloadComplete: boolean;
  uploadStatus: {
    currentFile: number;
    totalFiles: number;
  };
  setUploadStatus: (status: {
    currentFile: number;
    totalFiles: number;
  }) => void;
}

interface BlobUploadConfig {
  metadata: { [key: string]: string };
  tags: { [key: string]: string };
}

interface AppState {
  isUploading: boolean;
  fetchBlobsError: boolean;
  loadingBlobs: boolean;
  isMounted: boolean;
  downloadProgress: number | null;
  isDownloadComplete: boolean;
  uploadStatus: {
    currentFile: number;
    totalFiles: number;
  };
}

// Context
const UploadedFilesContext = createContext<
  UploadedFilesContextType | undefined
>(undefined);

// API Helper
const api = {
  async sendNotification(endpoint: string, body: unknown): Promise<Response> {
    const response = await fetch(`${window.location.origin}/api/${endpoint}`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      throw new Error(
        `Failed to send ${endpoint}. Status: ${response.status}. Message: ${response.statusText}`
      );
    }
    return response;
  },

  async uploadNotification(file: IDeliveredFile) {
    return this.sendNotification("uploadFileNotification", {
      DocumentCode: file.fileGuid,
      FileExtension: file.fileExtension,
      FileName: file.fileName,
      MimeType: file.mimetype,
      FileSize: file.size,
    });
  },

  async downloadNotification(blobGuid: string, containerName: string) {
    return this.sendNotification("downloadFileNotification", {
      ContainerName: containerName,
      DocumentCode: blobGuid,
    });
  },

  async deleteNotification(blobGuid: string) {
    return this.sendNotification("deleteFileNotification", {
      DocumentCode: blobGuid,
    });
  },

  async fetchUserBlobs() {
    const response = await fetch("/api/getUserBlobs", { cache: "no-store" });
    if (!response.ok) {
      throw new Error(`Failed to fetch blobs. Status: ${response.status}`);
    }
    return response.json();
  },
};

// Upload Helpers
const uploadHelpers = {
  createUploadConfig(
    file: IDeliveredFile,
    guid: string,
    candidateNumber: string,
    examGroupCode: string
  ): BlobUploadConfig {
    return {
      metadata: {
        fileextension: file.fileExtension,
        guid,
        filesize: file.size.toString(),
        checked: String(file.checked),
        filename: encodeURIComponent(file.fileName),
        mimetype: file.mimetype,
      },
      tags: {
        CandidateNumber: candidateNumber,
        ExamGroupCode: examGroupCode,
      },
    };
  },

  async uploadWithRetry(
    blockBlobClient: any,
    file: File,
    config: BlobUploadConfig,
    maxRetries = 5
  ): Promise<void> {
    let attempt = 0;
    while (attempt < maxRetries) {
      try {
        await blockBlobClient.uploadData(file, {
          metadata: config.metadata,
          tags: config.tags,
        });
        return;
      } catch (error) {
        attempt++;
        if (attempt === maxRetries) throw error;
        await new Promise((res) =>
          setTimeout(res, Math.pow(2, attempt) * 1000)
        );
      }
    }
  },
};

// Provider Component
export const UploadedFilesProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [uploadedFiles, setUploadedFiles] = useState<IDeliveredFile[]>([]);
  const [state, setState] = useState<AppState>({
    isUploading: false,
    fetchBlobsError: false,
    loadingBlobs: true,
    isMounted: false,
    downloadProgress: null,
    isDownloadComplete: true,
    uploadStatus: { currentFile: 0, totalFiles: 0 },
  });

  const updateFileInState = useCallback(
    (fileGuid: string, update: Partial<IDeliveredFile>) => {
      setUploadedFiles((prev) =>
        prev.map((f) => (f.fileGuid === fileGuid ? { ...f, ...update } : f))
      );
    },
    []
  );

  const isIOS = useCallback(() => {
    return (
      /iPad|iPhone|iPod/.test(navigator.userAgent) ||
      (navigator.userAgent.includes("Mac") && "ontouchend" in document)
    );
  }, []);

  useEffect(() => {
    if (state.isMounted) {
      const newRejectedFiles = uploadedFiles.filter((file) => file.isRejected);
      sessionStorage.setItem("rejectedFiles", JSON.stringify(newRejectedFiles));
      setState((prev) => ({
        ...prev,
        isUploading: uploadedFiles.some((file) => !file.uploadFinished),
      }));
    }
  }, [uploadedFiles, state.isMounted]);

  useEffect(() => {
    const fetchBlobs = async () => {
      try {
        const rejectedFiles = JSON.parse(
          sessionStorage.getItem("rejectedFiles") || "[]"
        );
        const data = await api.fetchUserBlobs();

        if (data.error) throw new Error(data.error);
        setUploadedFiles([...data.blobs, ...rejectedFiles]);
      } catch (error) {
        console.error("Failed to fetch blobs:", error);
        await logException(error as Error, {
          component: "UploadedFilesContext",
          action: "useEffect - fetchBlobs",
        });
        setState((prev) => ({ ...prev, fetchBlobsError: true }));
      } finally {
        setState((prev) => ({ ...prev, loadingBlobs: false, isMounted: true }));
      }
    };

    fetchBlobs();
  }, []);

  function getTestPart(testPartID: number) {
    switch (testPartID) {
      case TestPartsEnum.Eksamen:
        return "Eksamen";
      case TestPartsEnum.EksamenDel1:
        return "Eksamen del 1";
      case TestPartsEnum.EksamenDel2:
        return "Eksamen del 2";
      case TestPartsEnum.EksamenDel1ogDel2:
        return "Eksamen del 1 og del 2";
      default:
        return "Ikke angitt";
    }
  }

  const addFile = async (
    file: IDeliveredFile,
    candidateNumber: string,
    examGroupCode: string
  ): Promise<void> => {
    setUploadedFiles((prev) => [...prev, file]);

    if (!file.isRejected) {
      setState((prev) => ({
        ...prev,
        uploadStatus: {
          ...prev.uploadStatus,
          currentFile: prev.uploadStatus.currentFile + 1,
        },
      }));

      let sasTokenResponse: any = null;
      try {
        sasTokenResponse = await getUploadBlobSasToken();
        const blobClient = new BlobClient(sasTokenResponse.sastoken);
        const blockBlobClient = blobClient.getBlockBlobClient();

        const uploadConfig = uploadHelpers.createUploadConfig(
          file,
          sasTokenResponse.fileguid,
          candidateNumber,
          examGroupCode
        );

        if (file.file) {
          if (isIOS()) {
            await uploadHelpers.uploadWithRetry(
              blockBlobClient,
              file.file,
              uploadConfig
            );
          } else {
            await blockBlobClient.uploadData(file.file, uploadConfig);
          }
        }

        await api.uploadNotification({
          ...file,
          fileGuid: sasTokenResponse.fileguid,
        });
        updateFileInState(file.fileGuid, {
          uploadFinished: true,
          fileGuid: sasTokenResponse.fileguid,
        });

        await logActivity(
          file.fileName,
          getTestPart(file.testPartId),
          OperationEnum.LastetOpp
        );

        logEvent("uploadExamAnswer", {
          fileName: file.fileName,
          fileGuid: sasTokenResponse.fileguid,
          candidateNumber,
          examGroupCode,
          fileSize: file.size,
        });
      } catch (error) {
        console.error(`Error uploading file: ${file.fileName}`, error);

        await logException(error as Error, {
          component: "UploadedFilesContext",
          candidateNumber,
          examGroupCode,
          file: file.fileName,
          action: "addFile",
        });
        updateFileInState(file.fileGuid, {
          error: "upload-failed",
          uploadFinished: true,
          isRejected: true,
        });
      } finally {
        setState((prev) => {
          const newStatus =
            prev.uploadStatus.currentFile === prev.uploadStatus.totalFiles
              ? { currentFile: 0, totalFiles: 0 }
              : prev.uploadStatus;
          return { ...prev, uploadStatus: newStatus };
        });
      }
    }
  };

  const handleDownloadBlob = async (
    blobGuid: string,
    fileName: string,
    testPartId: number,
    onProgress?: (progress: number, completed: boolean) => void
  ) => {
    updateFileInState(blobGuid, { downloading: true });

    try {
      if (isIOS()) {
        setState((prev) => ({
          ...prev,
          isDownloadComplete: false,
          downloadProgress: 0,
        }));

        await api.downloadNotification(blobGuid, BESVARELSE_CONTAINER_NAME);

        await logActivity(
          fileName,
          getTestPart(testPartId),
          OperationEnum.SkjekketLastetNedFil
        );

        try {
          await getBlobIos(
            blobGuid,
            fileName,
            BESVARELSE_CONTAINER_NAME,
            true,
            (progress, completed) => {
              setState((prev) => ({ ...prev, downloadProgress: progress }));
              if (onProgress) onProgress(progress, completed);
              if (completed)
                setState((prev) => ({ ...prev, isDownloadComplete: true }));
            }
          );

          updateFileInState(blobGuid, {
            downloading: false,
            error: "",
            checked: true,
          });
        } catch (error) {
          console.error("Error downloading file:", error);
          updateFileInState(blobGuid, {
            downloading: false,
            error: "Failed to download file",
            checked: false,
          });
        }
      } else {
        await getBlob(blobGuid, fileName, BESVARELSE_CONTAINER_NAME, true);
        await api.downloadNotification(blobGuid, BESVARELSE_CONTAINER_NAME);
        await logActivity(
          fileName,
          getTestPart(testPartId),
          OperationEnum.SkjekketLastetNedFil
        );

        updateFileInState(blobGuid, {
          downloading: false,
          error: "",
          checked: true,
        });
      }

      logEvent("downloadExamAnswer", {
        fileName,
        fileGuid: blobGuid,
      });
    } catch (error) {
      console.error("Error downloading file:", error);
      await logException(error as Error, {
        component: "UploadedFilesContext",
        blobGuid,
        fileName,
        action: "handleDownloadBlob",
      });
      updateFileInState(blobGuid, {
        downloading: false,
        error: "download-failed",
      });

      if (isIOS()) {
        setState((prev) => ({ ...prev, isDownloadComplete: true }));
      }
    }
  };

  const handleDeleteFile = async (file: FileType) => {
    updateFileInState(file.fileGuid, { deleting: true });

    try {
      if (!file.isRejected) {
        await api.deleteNotification(file.fileGuid);

        const existingFile = uploadedFiles.find(
          (f) => f.fileGuid === file.fileGuid
        );

        await logActivity(
          existingFile?.fileName || file.fileGuid,
          getTestPart(existingFile?.testPartId || 0),
          OperationEnum.SlettetFil
        );

        setUploadedFiles((prevFiles) => {
          const newFiles = prevFiles.filter(
            (f) => f.fileGuid !== file.fileGuid
          );
          if (newFiles.filter((f) => !f.isRejected).length === 0) {
            fetch(`${window.location.origin}/api/lastFileDeleted`, {
              method: "GET",
              cache: "no-store",
            });
          }
          return newFiles;
        });
      } else {
        setUploadedFiles((prev) =>
          prev.filter((f) => f.fileGuid !== file.fileGuid)
        );
      }
    } catch (error) {
      console.error("Error deleting file:", error);
      await logException(error as Error, {
        component: "UploadedFilesContext",
        message: (error as Error).message,
        fileGuid: file.fileGuid,
        action: "handleDeleteFile",
      });
      updateFileInState(file.fileGuid, {
        error: "delete-failed",
        deleting: false,
      });
    } finally {
      updateFileInState(file.fileGuid, { deleting: false });
    }
  };

  const contextValue = {
    uploadedFiles,
    isUploading: state.isUploading,
    loadingBlobs: state.loadingBlobs,
    fetchBlobsError: state.fetchBlobsError,
    addFile,
    handleDownloadBlob,
    handleDeleteFile,
    downloadProgress: state.downloadProgress,
    isDownloadComplete: state.isDownloadComplete,
    uploadStatus: state.uploadStatus,
    setUploadStatus: (status: any) =>
      setState((prev) => ({ ...prev, uploadStatus: status })),
  };

  return (
    <UploadedFilesContext.Provider value={contextValue}>
      {children}
    </UploadedFilesContext.Provider>
  );
};

// Hook
export const useUploadedFiles = (): UploadedFilesContextType => {
  const context = useContext(UploadedFilesContext);
  if (!context) {
    throw new Error(
      "useUploadedFiles must be used within an UploadedFilesProvider"
    );
  }
  return context;
};
