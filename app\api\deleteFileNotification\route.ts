import { getAppInsightsServer } from "@/app/lib/appInsightsServer";
import { StatusInfoEmptyError } from "@/app/lib/exceptionTypes";
import { getClientIp } from "@/app/lib/getClientIp";
import { getPgsaAccessToken } from "@/app/lib/getPgsaAccesstoken";
import { getStatusInfoFromPgsa } from "@/app/lib/getStatusInfoFromPgsa";
import { getUserSessionData } from "@/app/lib/getUserSessionData";
import { getHashFromRedis } from "@/app/lib/redisHelper";
import { NextResponse } from "next/server";

// Adding interface that was implicit in the original code
interface IDeleteFileNotification {
  CandidateNumber: string;
  UserName: string;
  IPAddress: string;
  ExamGroupCode: string;
  DocumentCode: string;
  TestPartId: string;
  TestPeriod: string;
  SubjectCode: string;
}

const PgsaApiUrl = process.env.PGSA_API_ASYNC_URL;
const telemetryClient = getAppInsightsServer();

export async function POST(request: Request) {
  const userSessionData = await getUserSessionData();
  let response: Response | undefined;

  if (!userSessionData?.userSessionId)
    return NextResponse.json({ message: "Unauthorized" }, { status: 401 });

  // Use Redis hash to get only the isAuthorized field
  const userKey = `candidate:${userSessionData.candidateNumber}:${userSessionData.userSessionId}`;
  const isAuthorized = await getHashFromRedis(userKey, "isAuthorized");

  if (isAuthorized !== "true")
    return NextResponse.json(
      { message: "User not authorized" },
      { status: 403 }
    );

  const [statusInfo, deleteFile] = await Promise.all([
    getStatusInfoFromPgsa(userSessionData.userId),
    request.json(),
  ]);

  if (!statusInfo) throw new StatusInfoEmptyError();

  const [clientIp, pgsaAccessToken] = await Promise.all([
    getClientIp(),
    getPgsaAccessToken(),
  ]);

  const payload: IDeleteFileNotification = {
    CandidateNumber: statusInfo.CandidateNumber,
    UserName: statusInfo.UserId,
    IPAddress: clientIp,
    ExamGroupCode: statusInfo.ExamGroupCode,
    DocumentCode: deleteFile.DocumentCode,
    TestPartId: String(statusInfo.TestPartId),
    TestPeriod: statusInfo.TestPeriod,
    SubjectCode: statusInfo.SubjectCode,
  };

  try {
    response = await fetch(`${PgsaApiUrl}/api/exampaper/slett`, {
      method: "POST",
      body: JSON.stringify(payload),
      headers: {
        Authorization: `Bearer ${pgsaAccessToken}`,
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      throw new Error(
        `Feil ved sending av fil slett fil notifikasjon. Feilkode: ${
          response.status
        }, Feilmelding: ${await response.text()}`
      );
    }

    return new NextResponse("Success", { status: 200 });
  } catch (error) {
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        action: "deleteFileNotification",
        payload,
        response: response ? await response.text() : "Tom respons",
      },
    });
    console.error(error);
    return new NextResponse("An error occurred", { status: 500 });
  }
}
