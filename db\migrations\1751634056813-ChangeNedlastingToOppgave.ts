import { MigrationInterface, QueryRunner } from "typeorm";

export class ChangeNedlastingToOppgave1751634056813 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Change operation type from "Nedlasting" to "Oppgave" for operation 9
        await queryRunner.query(`
            UPDATE "Operation"
            SET "Operasjonstype" = 'Oppgave'
            WHERE "OperationID" = 9            
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Revert operation type from "Oppgave" back to "Nedlasting" for operation 9
        await queryRunner.query(`
            UPDATE "Operation"
            SET "Operasjonstype" = 'Nedlasting'
            WHERE "OperationID" = 9            
        `);
    }

}
