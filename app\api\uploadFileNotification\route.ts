"use server";
import { getClientIp } from "@/app/lib/getClientIp";
import { getPgsaAccessToken } from "@/app/lib/getPgsaAccesstoken";
import { getStatusInfoFromPgsa } from "@/app/lib/getStatusInfoFromPgsa";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import { StatusInfoEmptyError } from "@/app/lib/exceptionTypes";
import { getHashFromRedis } from "@/app/lib/redisHelper";
import { getUserSessionData } from "@/app/lib/getUserSessionData";
import { NextResponse } from "next/server";
import { getAllowedMimeTypes } from "@/app/lib/getAllowedMimeTypes";
import { getAppInsightsServer } from "@/app/lib/appInsightsServer";

dayjs.extend(utc);
const PgsaApiUrl = process.env.PGSA_API_ASYNC_URL;
const telemetryClient = getAppInsightsServer();

export async function POST(request: Request) {
  let response: any = null;
  let uploadFileNotification: IUploadFileNotification | null = null;
  try {
    const userSessionData = await getUserSessionData();
    if (!userSessionData?.userSessionId)
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });

    const userKey = `candidate:${userSessionData.candidateNumber}:${userSessionData.userSessionId}`;

    const [
      statusInfo,
      pgsaAccessToken,
      uploadedFile,
      clientIp,
      isAuthorized,
      mimeTypes,
    ] = await Promise.all([
      getStatusInfoFromPgsa(userSessionData.userId),
      getPgsaAccessToken(),
      request.json(),
      getClientIp(),
      getHashFromRedis(userKey, "isAuthorized"),
      getAllowedMimeTypes(),
    ]);

    if (!userSessionData?.userSessionId || isAuthorized !== "true")
      return NextResponse.json(
        { message: "User not authorized" },
        { status: 403 }
      );

    if (!statusInfo) throw new StatusInfoEmptyError();

    

    const mimeTypeObject = mimeTypes.filter(
      (file) =>
        file.FileExtension.toLowerCase() ===
        uploadedFile.FileExtension.toLowerCase()
    )[0];

    uploadFileNotification = {
      IpAddress: clientIp,
      SocialSecurityNumber: statusInfo.SocialSecurityNumber,
      DeliveryTime: dayjs().utc().toISOString(),
      CandidateNumber: statusInfo.CandidateNumber,
      ExamGroupCode: statusInfo.ExamGroupCode,
      FirstName: statusInfo.CandidateFirstName,
      SureName: statusInfo.CandidateSurname,
      UserName: statusInfo.CandidateNumber,
      UserId: statusInfo.UserId,
      SubjectName: statusInfo.SubjectName,
      SubjectCode: statusInfo.SubjectCode,
      SchoolId: statusInfo.SchoolId,
      Variant: statusInfo.Variant,
      TestPeriod: statusInfo.TestPeriod,
      TestType: statusInfo.TestType,
      TestPartId: statusInfo.TestPartId,
      DocumentCode: uploadedFile.DocumentCode,
      ExamStartTime: statusInfo.TestStartTime,
      FileExtension: uploadedFile.FileExtension,
      //   FileName: encodeURIComponent(uploadedFile.FileName),
      FileName: uploadedFile.FileName,
      MimeType: mimeTypeObject.MimeType,
      FileSize: uploadedFile.FileSize,
      Status: 0,
    };

    response = await fetch(`${PgsaApiUrl}/api/exampaper/status/lastetopp`, {
      method: "POST",
      body: JSON.stringify(uploadFileNotification),
      headers: {
        Authorization: `Bearer ${pgsaAccessToken}`,
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      throw new Error(
        `Feil ved sending av fil opplasting notifikasjon. Feilkode: ${response.status}, Feilmelding: ${response.statusText}`
      );
    }

    return new NextResponse();
  } catch (error) {
    if (error instanceof Error) {
      telemetryClient?.trackException({
        exception: error as Error,
        properties: {
          component: "uploadFileNotification",
          response: response ? await response.text() : "Empty response",
          uploadFileNotification,
        },
      });
    }
    throw error;
  }
}
