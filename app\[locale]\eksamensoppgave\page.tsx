import { getStatusInfoFromPgsa } from "@/app/lib/getStatusInfoFromPgsa";
import CandidateInfo from "@/components/candidateInfo";
import { getTranslations } from "next-intl/server";
import { getUserSessionData } from "@/app/lib/getUserSessionData";
import { StatusInfoEmptyError } from "@/app/lib/exceptionTypes";
import { Metadata } from "next";
import ExamInfo from "./components/ExamInfo";
import ExamFiles from "./components/ExamFiles";

export const metadata: Metadata = {
  title: "PGS - Eksamensoppgave",
  description: "Velkommen til eksamensoppgaven",
};

const EksamensOppgave = async ({}) => {
  const userSessionData = await getUserSessionData();

  const [statusInfo, t] = await Promise.all([
    getStatusInfoFromPgsa(userSessionData.userId),
    getTranslations("Eksamensoppgave"),
  ]);

  if (!statusInfo) throw new StatusInfoEmptyError();

  return (
    <>
      <div className="flex flex-col gap-6 mb-8">
        <h1 className="text-5xl font-bold">
          {t("WelcomeToExam")}
          {userSessionData.name}!
        </h1>
        <CandidateInfo
          candidateNumber={statusInfo.CandidateNumber}
          subjectCode={statusInfo.SubjectCode}
        />
      </div>
      <div className="flex flex-col lg:w-2/3 gap-8 mb-10">
        <div key="examInfo">
          <ExamInfo />
        </div>
        <div key="examFiles">
          <ExamFiles />
        </div>
      </div>
    </>
  );
};

export default EksamensOppgave;
