# TypeORM Migrations Guide

## Oversikt
Dette prosjektet bruker TypeORM for database migrations med miljø-spesifikke konfigurasjoner og dedikerte `.env` filer for hvert miljø.

## Miljø-spesifikke konfigurasjoner

### Data-source filer:
- `db/migrations/environments/migrations-data-source-local.ts` - <PERSON><PERSON> utvikling
- `db/migrations/environments/migrations-data-source-dev.ts` - <PERSON> miljø
- `db/migrations/environments/migrations-data-source-test.ts` - Test miljø
- `db/migrations/environments/migrations-data-source-qa.ts` - QA miljø
- `db/migrations/environments/migrations-data-source-production.ts` - Production miljø

### Miljø-filer (.env):
- `.env.local` - <PERSON>al utvikling (eksisterer allerede med NextAuth konfig)
- `.env.dev` - Dev miljø
- `.env.test` - Test miljø  
- `.env.qa` - QA miljø
- `.env.production` - Production miljø

**⚠️ VIKTIG:** `.env.dev`, `.env.test`, `.env.qa` og `.env.production` er i `.gitignore` og må opprettes lokalt med riktige verdier.

## Lokal utvikling

### Forutsetninger
Miljø-filene må inneholde database konfigurasjon:
```bash
DATABASE_HOST=your-host
DATABASE_USERNAME=your-username
DATABASE_PASSWORD=your-password
DATABASE_NAME=your-database
PGS_ENVIRONMENT=miljø-navn
```

### Tilgjengelige kommandoer

#### Via NPM scripts (miljø-spesifikke):
```bash
# Vis migration status
npm run migration:show:local
npm run migration:show:dev
npm run migration:show:test
npm run migration:show:qa
npm run migration:show:production

# Kjør alle pending migrations
npm run migration:run:local
npm run migration:run:dev
npm run migration:run:test
npm run migration:run:qa
npm run migration:run:production

# Revert siste migration
npm run migration:revert:local
npm run migration:revert:dev
npm run migration:revert:test
npm run migration:revert:qa
npm run migration:revert:production
```

#### Via PowerShell script (anbefalt):
```powershell
# Vis migration status for forskjellige miljøer
.\run-migrations.ps1 -Action show -Environment local
.\run-migrations.ps1 -Action show -Environment dev
.\run-migrations.ps1 -Action show -Environment test

# Kjør migrations (med bekreftelse)
.\run-migrations.ps1 -Action run -Environment local
.\run-migrations.ps1 -Action run -Environment dev

# Revert siste migration (med bekreftelse)
.\run-migrations.ps1 -Action revert -Environment local

# Generer ny migration
.\run-migrations.ps1 -Action generate -Environment local -Name "CreateUserTable"
```

## Hvordan det fungerer

### Forenklet arkitektur
Vi har nå kun **ett** PowerShell script:
- **`run-migrations.ps1`** - Hovedscript som håndterer alt

### NPM Scripts
NPM scripts kaller direkte `run-migrations.ps1`:
```json
"migration:run:dev": "pwsh -File run-migrations.ps1 -Action run -Environment dev"
```

### Script funksjonalitet
`run-migrations.ps1`:
1. Laster inn den spesifikke `.env` filen for miljøet
2. Setter miljøvariabler  
3. Kjører TypeORM kommandoen direkte med `npx typeorm-ts-node-esm`
4. Bruker riktig data-source fil automatisk

## Setup av miljøer

### For hver miljø må du opprette .env fil:

#### .env.dev
```bash
DATABASE_HOST=sql-pgs-dev.database.windows.net
DATABASE_USERNAME=your-dev-username
DATABASE_PASSWORD=your-dev-password
DATABASE_NAME=sqldb-pgs-dev
PGS_ENVIRONMENT=dev
```

#### .env.test
```bash
DATABASE_HOST=sql-pgs-test.database.windows.net
DATABASE_USERNAME=your-test-username
DATABASE_PASSWORD=your-test-password
DATABASE_NAME=sqldb-pgs-test
PGS_ENVIRONMENT=test
```

#### .env.qa
```bash
DATABASE_HOST=sql-pgs-qa.database.windows.net
DATABASE_USERNAME=your-qa-username
DATABASE_PASSWORD=your-qa-password
DATABASE_NAME=sqldb-pgs-qa
PGS_ENVIRONMENT=qa
```

#### .env.production
```bash
DATABASE_HOST=sql-pgs-prod.database.windows.net
DATABASE_USERNAME=your-prod-username
DATABASE_PASSWORD=your-prod-password
DATABASE_NAME=sqldb-pgs-prod
PGS_ENVIRONMENT=production
```

## GitHub Actions

GitHub Actions workflow bruker fortsatt miljøvariabler konfigurert i repository settings, ikke .env filene som er for lokal utvikling.

### Miljøvariabler som må være konfigurert i GitHub:

#### Repository Variables:
- `DATABASE_HOST`: Database server host
- `DATABASE_NAME`: Database navn

#### Repository Secrets:
- `DATABASE_USERNAME`: Database brukernavn
- `DATABASE_PASSWORD`: Database passord

## Sikkerhet og beste praksis

### Lokalt
- **ALDRI** commit `.env.dev`, `.env.test`, `.env.qa` eller `.env.production` til git
- Disse filene er i `.gitignore` 
- Bruk sterke passord for database tilkobling
- Test migrations på lokal kopi av database først

### GitHub Actions
- Bruk GitHub Environments for å beskytte produksjon
- Krev manual godkjenning for produksjon deployments
- Alltid sjekk pending migrations før kjøring
- Ha backup av database før kjøring i produksjon

## Eksempel workflow

### Lokal utvikling:
1. Opprett `.env.local` hvis den ikke eksisterer (eller bruk eksisterende)
2. Gjør endringer i entities
3. Generer migration: `.\run-migrations.ps1 -Action generate -Environment local -Name "UpdateUserTable"`
4. Sjekk generert migration fil
5. Test lokalt: `.\run-migrations.ps1 -Action run -Environment local`
6. Commit migration fil (men ikke .env filene)

### Deploy til test:
1. Opprett `.env.test` med riktige verdier for test miljø
2. Test migrations: `.\run-migrations.ps1 -Action show -Environment test`
3. Kjør migrations: `.\run-migrations.ps1 -Action run -Environment test`
4. Push endringer til GitHub
5. Kjør GitHub Actions workflow for test miljø

## Feilsøking

### Vanlige problemer:
- **Missing .env file**: Opprett riktig .env fil for miljøet
- **Connection timeout**: Sjekk database tilkobling og firewall regler
- **Permission denied**: Sjekk database bruker har riktige rettigheter
- **Migration already exists**: Sjekk at migration ikke allerede er kjørt
- **Wrong environment**: Sjekk at du bruker riktig miljø parameter

### Debugging:
- Bruk `migration:show:miljø` for å se status
- Sjekk database logs
- Verifiser at riktig .env fil lastes
- Sjekk at riktig data-source fil brukes for miljøet
- Kontroller miljøvariabler er satt korrekt

## Fordeler med denne løsningen

✅ **Miljø-spesifikke konfigurasjoner** - Hver miljø har sin egen .env fil  
✅ **Sikkerhet** - Sensitive data ikke i kildekode eller git  
✅ **Fleksibilitet** - Enkelt å skifte mellom miljøer  
✅ **Konsistens** - Samme kommandoer for alle miljøer  
✅ **GitHub Actions support** - Fungerer både lokalt og i CI/CD
