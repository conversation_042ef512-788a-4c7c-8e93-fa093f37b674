"use client";
import { Audience } from "@/app/enums/Audience";
import { useState } from "react";
import { v4 as uuidv4 } from "uuid";

const SendMessageToSignalR = ({}) => {
  const [candidateNumber, setCandidateNumber] = useState("");
  const [groupCode, setGroupCode] = useState("");
  const [messageId, setMessageId] = useState("");

  return null;
  return (
    <div className="App">
      <div className="App"></div>
      <div className="flex flex-col gap-10">
        <div className="flex flex-col gap-10">
          <div>
            <button
              className="btn btn-primary"
              onClick={async () => {
                const message = {
                  id: uuidv4(),
                  message: "Hello from SignalR to all!",
                  audience: Audience.All,
                };
                const response = await fetch(
                  `${window.location.origin}/api/signalRMessages`,
                  {
                    method: "POST",
                    body: JSON.stringify(message),
                  }
                );

                console.log("Response from SignalR:", await response.json());
              }}
            >
              Send message to all
            </button>
          </div>

          <div className="flex gap-4">
            <input
              className="form-control"
              type="text"
              value={groupCode}
              onChange={(e) => setGroupCode(e.target.value)}
            />

            <button
              className="btn btn-primary"
              onClick={async () => {
                const message = {
                  id: uuidv4(),
                  message: "Hello from SignalR to group!",
                  audience: Audience.Group,
                  audienceId: groupCode,
                };

                const response = await fetch(
                  `${window.location.origin}/api/signalRMessages`,
                  {
                    method: "POST",
                    body: JSON.stringify(message),
                  }
                );

                console.log(
                  "Response from SignalR:",
                  response.status,
                  response.statusText
                );
              }}
            >
              Send message to group
            </button>
          </div>

          <div className="flex gap-4">
            <input
              type="text"
              value={candidateNumber}
              onChange={(e) => setCandidateNumber(e.target.value)}
            />

            <button
              className="btn btn-primary"
              onClick={async () => {
                const message = {
                  id: uuidv4(),
                  message: "Hello from SignalR to single user!",
                  audience: Audience.User,
                  audienceId: candidateNumber,
                };
                const response = await fetch(
                  `${window.location.origin}/api/signalRMessages`,
                  {
                    method: "POST",
                    body: JSON.stringify(message),
                  }
                );
              }}
            >
              Send message to single user
            </button>
          </div>
          <div className="flex gap-4">
            <input
              className="form-control"
              type="text"
              value={messageId}
              onChange={(e) => setMessageId(e.target.value)}
            />

            <button
              className="btn btn-primary"
              onClick={async () => {
                const response = await fetch(
                  `${window.location.origin}/api/signalRMessages`,
                  {
                    method: "DELETE",
                    body: JSON.stringify({
                      id: messageId,
                    }),
                  }
                );

                console.log("Response from SignalR:", await response.json());
              }}
            >
              Delete message
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SendMessageToSignalR;
