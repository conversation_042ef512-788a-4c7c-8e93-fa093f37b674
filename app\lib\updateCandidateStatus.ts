"use server";

import dayjs from "dayjs";
import { ICandidateStatus } from "../interfaces/ICandidateStatus";
import { getPgsaAccessToken } from "./getPgsaAccesstoken";
import { getUserSessionData } from "./getUserSessionData";
import { StatusUpdateError } from "./exceptionTypes";
import utc from "dayjs/plugin/utc";
import { getAppInsightsServer } from "./appInsightsServer";

const PgsaApiUrl = process.env.PGSA_API_ASYNC_URL;
const updateStatusApiUrl = `${PgsaApiUrl}/api/candidate/Status`;
const telemetryClient = getAppInsightsServer();

dayjs.extend(utc);

export async function updateCandidateStatus(
  currentStatus: number,
  newStatus: number,
  testPartID: number,
  candidateNumber: string,
  examGroupCode: string,
  username: string,
  userId: string,
  ipAddress: string
): Promise<Response> {
  let updateBody,
    baseUpdateBody: ICandidateStatus | null = null;
  let response: Response | null = null;

  try {
    const pgsaAccessToken = await getPgsaAccessToken();

    baseUpdateBody = {
      CandidateNumber: candidateNumber,
      ExamGroupCode: examGroupCode,
      NewCandidateStatus: newStatus,
      CurrentCandidateStatus: currentStatus,
      TestPartId: testPartID,
      Username: username,
      Timestamp: dayjs().utc().toISOString(),
      UserId: userId,
    };

    updateBody = ipAddress?.trim()
      ? { ...baseUpdateBody, IpAddress: ipAddress }
      : baseUpdateBody;

    response = await fetch(updateStatusApiUrl, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${pgsaAccessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(updateBody),
      cache: "no-store",
    });

    if (!response.ok) {
      throw new StatusUpdateError(
        `Feil ved oppdatering av status i PGSA. Feilkode: ${
          response.status
        }. Feilmelding: ${response.statusText}. Paylod: ${JSON.stringify(
          updateBody
        )}`
      );
    }

    return response;
  } catch (error) {
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        component: "updateCandidateStatus",
        action: "UpdateStatus",
        updateBody: JSON.stringify(updateBody),
        responseStatus: response?.status,
        responseStatusText: response?.statusText,
      },
    });
    console.error("Error in updateCandidateStatus:", error);
    throw error;
  } finally {
  }
}
