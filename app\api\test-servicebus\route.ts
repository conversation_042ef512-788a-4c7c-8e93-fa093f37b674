import { NextRequest, NextResponse } from "next/server";
import { 
  testServiceBusConnection, 
  getServiceBusStatus,
  sendAuditLogToServiceBus 
} from "@/app/lib/serviceBusClient";
import { OperationEnum } from "@/app/enums/OperationEnum";
import { IAuditLogData } from "@/app/interfaces/IAuditLogData";

export async function GET(request: NextRequest) {
  try {
    console.log("=== Service Bus Test Endpoint ===");
    
    // Test connection
    const connectionTest = await testServiceBusConnection();
    console.log("Connection test result:", connectionTest);
    
    // Get status
    const status = getServiceBusStatus();
    console.log("Service Bus status:", status);
    
    return NextResponse.json({
      success: true,
      connectionTest,
      status,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error("Service Bus test endpoint error:", error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log("=== Service Bus Test Message Send ===");
    
    // Create test audit log data
    const testData: IAuditLogData = {
      kandidatpaameldingId: "test-12345",
      rolle: "test-kandidat",
      kandidatNr: "12345678901",
      kandidatFornavn: "Test",
      kandidatEtternavn: "User",
      sesjonsId: "test-session-123",
      ip: "127.0.0.1",
      operationId: OperationEnum.TilgangBedt,
      parameters: { test: true },
      eksamensdel: "Test Del",
      filnavn: "test.pdf"
    };
    
    const userAgent = request.headers.get("user-agent") || "Test User Agent";
    
    console.log("Sending test audit log:", testData);
    
    // Test sending message with timeout
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Test timeout after 5 seconds')), 5000);
    });

    await Promise.race([
      sendAuditLogToServiceBus(testData, userAgent),
      timeoutPromise
    ]);
    
    console.log("Test message sent successfully");
    
    return NextResponse.json({
      success: true,
      message: "Test audit log sent successfully",
      testData,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error("Service Bus test message send error:", error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
