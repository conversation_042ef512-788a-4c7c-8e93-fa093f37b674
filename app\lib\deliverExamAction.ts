"use server";
import { getStatusInfoFromPgsa } from "./getStatusInfoFromPgsa";
import { getPgsaAccessToken } from "./getPgsaAccesstoken";
import { getClientIp } from "./getClientIp";
import { StatusInfoError } from "./exceptionTypes";
import { CandidateStatusEnum } from "../enums/CandidateStatusEnum";
import { getAppInsightsServer } from "./appInsightsServer";
import { redirect } from "next/navigation";
import { RouteEnum } from "../enums/RouteEnum";
import { getUserSessionData } from "./getUserSessionData";
import { isSetMember } from "./redisHelper";
import AuditLogService from "@/db/services/auditLogService";
import { getUserLoggingInfo } from "./getUserLoggingInfo";
import { OperationEnum } from "../enums/OperationEnum";
import test from "node:test";
import { TestPartsEnum } from "../enums/TestParts";
import { sendAuditLogsBatch } from "./serviceBusClient";
import { headers } from "next/headers";

const PgsaApiUrl = process.env.PGSA_API_ASYNC_URL;
const telemetryClient = getAppInsightsServer();

function formatDeliveredLogMessage(template: string, count: number): string {
  const word = count === 1 ? "1 fil" : `${count} filer`;
  return template.replace("x filer", word);
}

function getTestPart(testPartID: number) {
  switch (testPartID) {
    case TestPartsEnum.Eksamen:
      return "Eksamen";
    case TestPartsEnum.EksamenDel1:
      return "Eksamen del 1";
    case TestPartsEnum.EksamenDel2:
      return "Eksamen del 2";
    case TestPartsEnum.EksamenDel1ogDel2:
      return "Eksamen del 1 og del 2";
    default:
      return "Ikke angitt";
  }
}

export async function deliverExamAction(prevState: any, formData: FormData) {
  const userSessionData = await getUserSessionData();

  const [statusInfo, pgsaAccessToken, clientIp] = await Promise.all([
    getStatusInfoFromPgsa(userSessionData.userId),
    getPgsaAccessToken(),
    getClientIp(),
  ]);
  if (!statusInfo) throw new StatusInfoError();

  // Check if user is blocked after we have statusInfo
  const isBlocked = await isSetMember(statusInfo.UserId, statusInfo.SchoolId);

  if (isBlocked) {
    redirect(RouteEnum.IkkeTilgang);
  }

  /*const tracer = trace.getTracer("DeliverExamFiles");
  let span = tracer.startSpan("DeliverExamFiles - PGSA-request");*/

  let response: Response | null = null;
  let deliverExam: IDeliverExam | null = null;

  try {
    if (statusInfo.Status !== CandidateStatusEnum.LastetOpp) {
      return {
        delivered: false,
        errorMessage: "Bruker har ikke lov til å levere eksamen",
        userStatus: statusInfo.Status,
      };
    }

    const uploadedFilesString = formData.get("uploadedFiles") as string; // Get the string value

    // Convert the string value to a JSON object
    const uploadedFilesArray = JSON.parse(uploadedFilesString) as any[];

    console.log("uploadedFilesArray", uploadedFilesArray);

    const body: IDeliverFilesNotification = {
      DocumentCode: uploadedFilesArray.map((file) => file.fileGuid),
    };

    deliverExam = {
      CandidateNumber: statusInfo.CandidateNumber,
      ExamGroupCode: statusInfo.ExamGroupCode,
      DocumentCodes: body.DocumentCode,
      TestPartId: statusInfo.TestPartId,
      UserName: statusInfo.CandidateNumber,
      UserId: statusInfo.UserId,
      IpAddress: clientIp,
    };
    response = await fetch(`${PgsaApiUrl}/api/exampaper/status/levert`, {
      method: "POST",
      body: JSON.stringify(deliverExam),
      headers: {
        Authorization: `Bearer ${pgsaAccessToken}`,
        "Content-Type": "application/json",
      },
    });
    if (!response.ok) {
      throw new Error(
        `Klarte ikke å levere eksamen for bruker. Feilkode: ${
          response.status
        }. Payload: ${JSON.stringify(
          deliverExam
        )} Feilmelding: ${await response.text()}`
      );
    }

    const operationId =
      statusInfo.TestPartId === 1
        ? OperationEnum.LevertDigitalt
        : OperationEnum.LevertDigitaltDel2;

    const headersList = await headers();
    const userAgent = headersList.get("user-agent") || "Unknown User Agent";
    const fileListString = uploadedFilesArray
      .map((file) => `${file.fileName} - (${file.fileGuid})`)
      .join("<br/>");

    // Send to service bus using batch for better performance
    // First execute delivery logging and file logging
    const initialLoggingPromises = [
      getUserLoggingInfo(
        operationId,
        { antallFiler: uploadedFilesArray.length },
        getTestPart(statusInfo.TestPartId),
        fileListString
      ),
      // Add logging for each individual file
      ...uploadedFilesArray.map((file) =>
        getUserLoggingInfo(
          OperationEnum.FilenInnlevert,
          undefined,
          getTestPart(statusInfo.TestPartId),
          `${file.fileName} - (${file.fileGuid})`
        )
      ),
    ];

    const initialResults = await Promise.all(initialLoggingPromises);
    const [deliveryData, ...fileLoggingData] = initialResults;

    // Execute status logging last
    const statusData = await getUserLoggingInfo(
      statusInfo.TestPartId === 1
        ? OperationEnum.StatusLevertDigitalt
        : OperationEnum.StatusLevertDigitaltDel2
    );

    // Batch all audit logs together for efficiency
    const auditLogs = [];
    if (deliveryData) {
      auditLogs.push({ data: deliveryData, userAgent });
    } else {
      console.error("Failed to build delivery audit log data");
    }

    if (statusData) {
      auditLogs.push({ data: statusData, userAgent });
    } else {
      console.error("Failed to build status audit log data");
    }

    // Add file logging data to audit logs
    fileLoggingData.forEach((fileData, index) => {
      if (fileData) {
        auditLogs.push({ data: fileData, userAgent });
      } else {
        console.error(`Failed to build file audit log data for file ${index}`);
      }
    });

    // Send both logs in a single batch operation
    if (auditLogs.length > 0) {
      await sendAuditLogsBatch(auditLogs);
    }

    redirect(RouteEnum.Kvittering);
  } catch (error) {
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        component: "deliverExamAction",
        action: "DeliverExam",
        candidateNumber: statusInfo?.CandidateNumber,
        userGuid: statusInfo?.UserId,
        examGroupCode: statusInfo?.ExamGroupCode,
        response: response ? await response.text() : "Tom respons",
        statuscode: response ? response.status : "Tom statuskode",
      },
    });
    console.error("Error in deliverExamAction:", error);
    throw error;
  } finally {
    redirect(RouteEnum.Kvittering);
  }
}
