import jwt from "jsonwebtoken";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../auth/authOptions";
import { getUserSessionData } from "@/app/lib/getUserSessionData";
import { generateSignalRAccessToken } from "@/app/lib/generateSignalRToken";
import { getAppInsightsServer } from "@/app/lib/appInsightsServer";

const connectionString =
  process.env.CUSTOMCONNSTR_AZURE_SIGNALR_CONNECTION_STRING || "";
const hubName = process.env.AZURE_SIGNALR_HUB_NAME || "pgshub";
const endpoint = connectionString?.match(/Endpoint=(.*?);/)?.[1];
const accessKey = connectionString?.match(/AccessKey=(.*?);/)?.[1] ?? "";

// Initialize Application Insights client once and cache it
const appInsights = getAppInsightsServer();

async function addUserToGroup(groupName: string, userId: string) {
  const url = `${endpoint}/api/v1/hubs/${hubName}/groups/${groupName}/users/${userId}`;

  await fetch(url, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${generateSignalRAccessToken({
        audience: url,
        lifetime: 60,
      })}`,
    },
  });
}

export async function POST(request: Request) {
  let session: any = null;
  let sessionData: any = null;

  try {
    // 1. Validate session exists first
    session = await getServerSession(authOptions);
    if (!session?.user?.userInfo?.userSessionId) {
      const errorDetails = {
        sessionExists: !!session,
        userExists: !!session?.user,
        userInfoExists: !!session?.user?.userInfo,
        userSessionId: session?.user?.userInfo?.userSessionId,
        endpoint: "negotiate",
        reason: "no_valid_session"
      };
      
      console.error("Negotiate: No valid session found", errorDetails);
      
      // Log 401 error to Application Insights
      try {
        appInsights.trackEvent({
          name: "Negotiate_Unauthorized",
          properties: {
            ...errorDetails,
            message: "SignalR negotiate endpoint received request without valid session",
            httpStatusCode: "401",
            timestamp: new Date().toISOString()
          }
        });
      } catch (insightsError) {
        console.error("Failed to log to Application Insights:", insightsError);
      }
      
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    sessionData = await getUserSessionData();

    // Additional validation of session data
    if (
      !sessionData?.userSessionId ||
      !sessionData?.candidateNumber ||
      !sessionData?.candidateGroupCode
    ) {
      const errorDetails = {
        userSessionId: !!sessionData?.userSessionId,
        candidateNumber: !!sessionData?.candidateNumber,
        candidateGroupCode: !!sessionData?.candidateGroupCode,
        sessionExists: !!session,
        endpoint: "negotiate",
        reason: "incomplete_session_data"
      };
      
      console.error("Negotiate: Incomplete session data", errorDetails);
      
      // Log 401 error to Application Insights
      try {
        appInsights.trackEvent({
          name: "Negotiate_Unauthorized",
          properties: {
            ...errorDetails,
            message: "SignalR negotiate endpoint received request with incomplete session data",
            httpStatusCode: "401",
            timestamp: new Date().toISOString()
          }
        });
      } catch (insightsError) {
        console.error("Failed to log to Application Insights:", insightsError);
      }
      
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    // 2. Environment variable validation
    if (!connectionString || !endpoint || !accessKey) {
      console.error("Negotiate: Missing SignalR configuration", {
        hasConnectionString: !!connectionString,
        hasEndpoint: !!endpoint,
        hasAccessKey: !!accessKey
      });
      return NextResponse.json({ error: "SignalR configuration missing" }, { status: 500 });
    }

    // Generate the token
    const token = jwt.sign(
      {
        aud: `${endpoint}/client/?hub=${hubName}`,
        exp: Math.floor(Date.now() / 1000) + 60 * 15, // Token expiration time in seconds (15 minutes)
        sub: sessionData.candidateNumber,
      },
      accessKey
    );

    await addUserToGroup(
      sessionData.candidateGroupCode,
      sessionData.candidateNumber
    );

    return NextResponse.json(
      { url: `${endpoint}/client/?hub=${hubName}`, accessToken: token },
      { status: 200 }
    );
  } catch (error) {
    return NextResponse.json({ error: "Failed to negotiate" }, { status: 500 });
  }
}
