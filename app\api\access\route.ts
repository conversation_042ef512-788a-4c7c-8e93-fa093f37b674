import { CandidateStatusEnum } from "@/app/enums/CandidateStatusEnum";
import { RouteEnum } from "@/app/enums/RouteEnum";
import { IStatusInfo } from "@/app/interfaces/IStatusInfo";
import { IUserSessionData } from "@/app/interfaces/IUserSessionData";
import { getAppInsightsServer } from "@/app/lib/appInsightsServer";
import { deauthorizeUserObjectRedis } from "@/app/lib/deauthorizeUserObjectRedis";
import { getStatusInfoFromPgsa } from "@/app/lib/getStatusInfoFromPgsa";
import { initializeCandidate } from "@/app/lib/initializeCandidate";
import { isCandidateBlocked } from "@/app/lib/isCandidateBlocked";
import { getUserInfoFromRedis } from "@/app/lib/redisHelper";
import { updateCandidateStatus } from "@/app/lib/updateCandidateStatus";
import dayjs from "dayjs";
import { NextRequest, NextResponse } from "next/server";

const telemetryClient = getAppInsightsServer();

async function handleCandidateStatus(
  finalStatus: IStatusInfo,
  candidateNumber: string,
  userInfoFromRedis: IUserSessionData,
  currentRoute: RouteEnum
): Promise<RouteEnum> {
  const examStarted = dayjs.utc().isAfter(dayjs.utc(finalStatus.TestStartTime));

  switch (finalStatus.Status) {
    case CandidateStatusEnum.InnloggetAutentisert:
    case CandidateStatusEnum.LastetOpp:
      if (!userInfoFromRedis.isAuthorized) return RouteEnum.Hjem;
      else if (!examStarted) return RouteEnum.Klar;
      else if (
        currentRoute !== RouteEnum.Eksamensoppgave &&
        currentRoute !== RouteEnum.Levering
      ) {
        return RouteEnum.Eksamensoppgave;
      } else return currentRoute;

    case CandidateStatusEnum.Levert:
    case CandidateStatusEnum.LevertManuelt:
      await deauthorizeUserObjectRedis(candidateNumber);
      return RouteEnum.Kvittering;

    case CandidateStatusEnum.DokumentertFravaer:
    case CandidateStatusEnum.IkkeDokumentertFravaer:
    case CandidateStatusEnum.SkalLeverePaPapir:
      await deauthorizeUserObjectRedis(candidateNumber);
      return RouteEnum.Fravaer;

    case CandidateStatusEnum.VenterPaaDagspassordIkkeAutentisert:
    case CandidateStatusEnum.IkkeInnlogget:
      return RouteEnum.Hjem;

    default:
      return RouteEnum.Hjem;
  }
}

export async function POST(request: NextRequest) {
  const { currentRoute, userSessionId, candidateNumber, userId, clientIp } =
    await request.json();

  try {
    let [userInfoFromRedis, userStatusFromPgsa] = await Promise.all([
      getUserInfoFromRedis(userSessionId, candidateNumber),
      getStatusInfoFromPgsa(userId),
    ]);

    // Hvis userStatusFromPgsa er null, initialiser kandidaten og hent status på nytt
    if (!userStatusFromPgsa) {
      await initializeCandidate(userInfoFromRedis);
      userStatusFromPgsa = await getStatusInfoFromPgsa(userId);
    }

    // Hvis userStatusFromPgsa fortsatt er null, omdiriger til Hjem
    if (!userStatusFromPgsa) {
      return NextResponse.json({ redirect: RouteEnum.Hjem });
    }

    // Oppdater status hvis kandidaten ikke er innlogget
    if (userStatusFromPgsa.Status === CandidateStatusEnum.IkkeInnlogget) {
      await updateCandidateStatus(
        CandidateStatusEnum.IkkeInnlogget,
        CandidateStatusEnum.VenterPaaDagspassordIkkeAutentisert,
        userStatusFromPgsa.TestPartId,
        userStatusFromPgsa.CandidateNumber,
        userStatusFromPgsa.ExamGroupCode,
        userStatusFromPgsa.CandidateNumber,
        userStatusFromPgsa.UserId,
        clientIp
      );
      userStatusFromPgsa = await getStatusInfoFromPgsa(userId);

      if (!userStatusFromPgsa) {
        return NextResponse.json({ redirect: RouteEnum.Hjem });
      }
    }

    // Oppdater status hvis brukeren er autorisert og venter på dagpassord
    if (
      userInfoFromRedis.isAuthorized &&
      userStatusFromPgsa.Status ===
        CandidateStatusEnum.VenterPaaDagspassordIkkeAutentisert
    ) {
      await updateCandidateStatus(
        userStatusFromPgsa.Status,
        CandidateStatusEnum.InnloggetAutentisert,
        userStatusFromPgsa.TestPartId,
        userStatusFromPgsa.CandidateNumber,
        userStatusFromPgsa.ExamGroupCode,
        userStatusFromPgsa.CandidateNumber,
        userStatusFromPgsa.UserId,
        clientIp
      );
      userStatusFromPgsa = await getStatusInfoFromPgsa(userId);

      if (!userStatusFromPgsa) {
        return NextResponse.json({ redirect: RouteEnum.Hjem });
      }
    }

    // Sjekk om eksamen er papirbasert
    const isBlocked = await isCandidateBlocked(
      userStatusFromPgsa.UserId,
      userStatusFromPgsa.SchoolId
    );

    if (
      isBlocked &&
      ![
        CandidateStatusEnum.Levert,
        CandidateStatusEnum.LevertManuelt,
        CandidateStatusEnum.DokumentertFravaer,
        CandidateStatusEnum.IkkeDokumentertFravaer,
      ].includes(userStatusFromPgsa.Status)
    ) {
      return NextResponse.json({ redirect: RouteEnum.IkkeTilgang });
    }

    // Håndter kandidatstatus og omdirigering
    const redirectRoute = await handleCandidateStatus(
      userStatusFromPgsa,
      candidateNumber,
      userInfoFromRedis,
      currentRoute
    );

    return NextResponse.json({ redirect: redirectRoute });
  } catch (error) {
    console.error("Error in authRoute", error);
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        component: "authRoute",
        action: "NavigationCheck",
        candidateNumber: candidateNumber,
        userGuid: userSessionId,
        currentRoute,
      },
    });
    return NextResponse.json({ redirect: RouteEnum.Hjem });
  }
}
