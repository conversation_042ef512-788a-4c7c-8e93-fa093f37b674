import dayjs from "dayjs";
import { CandidateStatusEnum } from "../enums/CandidateStatusEnum";
import { RouteEnum } from "../enums/RouteEnum";
import { IStatusInfo } from "../interfaces/IStatusInfo";
import { IUserSessionData } from "../interfaces/IUserSessionData";

export const getRoute = (
  finalStatus: IStatusInfo,
  userInfoFromRedis: IUserSessionData
) => {
  try {
    const examStarted = dayjs
      .utc()
      .isAfter(dayjs.utc(finalStatus.TestStartTime));

    switch (finalStatus.Status) {
      case CandidateStatusEnum.IkkeInnlogget:
      case CandidateStatusEnum.VenterPaaDagspassordIkkeAutentisert:
        return "autoriseringssiden";
      case CandidateStatusEnum.InnloggetAutentisert:
      case CandidateStatusEnum.LastetOpp:
        if (userInfoFromRedis && !userInfoFromRedis.isAuthorized)
          return "autoriseringssiden";
        else if (!examStarted) return "ventesiden";
        else return "eksamensoppgavesiden";
      case CandidateStatusEnum.Levert:
      case CandidateStatusEnum.LevertManuelt:
        return "kvitteringssiden";
      case CandidateStatusEnum.DokumentertFravaer:
      case CandidateStatusEnum.IkkeDokumentertFravaer:
      case CandidateStatusEnum.SkalLeverePaPapir:
        return "fraværssiden";
      case CandidateStatusEnum.VenterPaaDagspassordIkkeAutentisert:
      case CandidateStatusEnum.IkkeInnlogget:
        return "autoriseringssiden";

      default:
        return RouteEnum.Hjem;
    }
  } catch (error) {
    console.error("Error in getRoute:", error);
    return "";
  }
};
