"use server";

import { IDeliveredFile } from "../interfaces/IUploadFile";
import { getAppInsightsServer } from "./appInsightsServer";
import { getPgsaAccessToken } from "./getPgsaAccesstoken";

const PgsaApiUrl = process.env.PGSA_API_ASYNC_URL;

const telemetryClient = getAppInsightsServer();

export const getDeliveredFiles = async (
  candidateNumber: string,
  groupcode: string,
  receiptPage?: boolean
): Promise<IDeliveredFile[]> => {
  let response: Response | null = null;

  try {
    const pgsaAccessToken = await getPgsaAccessToken();

    response = await fetch(
      `${PgsaApiUrl}/api/exampaper/uploaded/list/${candidateNumber}/${groupcode}${
        receiptPage ? "/3" : ""
      }`,
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${pgsaAccessToken}`,
          "Content-Type": "application/json",
        },
        cache: "no-store",
      }
    );

    if (!response.ok) {
      if (response.status === 404) {
        return []; // Ingen filer funnet, returner tom liste
      }
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const jsonResponse = await response.json();
    const data: IDeliveredFile[] = jsonResponse.data;

    if (!Array.isArray(data)) {
      throw new Error("Uventet dataformat fra API");
    }

    return data.map((item) => ({
      ...item,
      uploadFinished: true,
      isRejected: false,
      downloading: false,
    }));
  } catch (error) {
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        Message: "Error in getDeliveredFiles",
        Response: response ? await response.text() : "Tom respons",
        Statuscode: response ? response.status : "Tom statuskode",
      },
    });

    if (error instanceof Error) {
      throw new Error(`Feil ved henting av opplastede filer: ${error.message}`);
    }
    throw new Error("En ukjent feil oppstod ved henting av opplastede filer");
  }
};
