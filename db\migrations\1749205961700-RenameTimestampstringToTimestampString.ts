import { MigrationInterface, QueryRunner } from "typeorm";

export class RenameTimestampstringToTimestampString1749205961700 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        // Check if the column exists with the old name (case-sensitive)
        const oldResult = await queryRunner.query(`
            SELECT COUNT(*) as count
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'AuditLog'
            AND COLUMN_NAME = 'Timestampstring'
        `);
        
        // Check if the column exists with the new name (case-sensitive)
        const newResult = await queryRunner.query(`
            SELECT COUNT(*) as count
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'AuditLog'
            AND COLUMN_NAME = 'TimestampString'
        `);
        
        if (oldResult[0].count > 0 && newResult[0].count === 0) {
            await queryRunner.query(`
                EXEC sp_rename 'AuditLog.Timestampstring', 'TimestampString', 'COLUMN'
            `);
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Check if the column exists with the new name (case-sensitive)
        const newResult = await queryRunner.query(`
            SELECT COUNT(*) as count
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'AuditLog'
            AND COLUMN_NAME = 'TimestampString'
        `);
        
        // Check if the column exists with the old name (case-sensitive)
        const oldResult = await queryRunner.query(`
            SELECT COUNT(*) as count
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'AuditLog'
            AND COLUMN_NAME = 'Timestampstring'
        `);
        
        if (newResult[0].count > 0 && oldResult[0].count === 0) {
            await queryRunner.query(`
                EXEC sp_rename 'AuditLog.TimestampString', 'Timestampstring', 'COLUMN'
            `);
        }
    }
}
