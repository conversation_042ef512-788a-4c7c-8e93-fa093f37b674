# PGSA v3.0 Datamodell Migration

Dette migration scriptet implementerer den forenklede datamodellen for PGSA v3.0 basert på det vedlagte diagrammet.

## Datamodell struktur

Migration scriptet oppretter følgende tabeller med relasjoner:

### Hovedtabeller:

1. **Person** - Grunnleggende personinformasjon
   - Fødselsnummer (PK)
   - Fornavn, Etternavn
   - CreatedDate, ModifiedDate

2. **Kandidatpaamelding** - Kandidatens påmelding til eksamen
   - KandidatpaameldingID (PK)
   - Fødselsnummer (FK til Person)
   - KandidatNummer, Fagkode, Årgang
   - Gjelder, Kommentar

3. **Kandidatperiode** - Kandidatens perioder
   - KandidatperiodeID (PK)
   - KandidatpaameldingID (FK)
   - Årgang, Gjennomføringsdato, Gyldig

4. **Eksamensperiode** - Eksamensperioder
   - EksamensperiodeID (PK)
   - KandidatpaameldingID (FK)
   - Fag<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Gjennomføringsdato
   - Gjennomføringsform, Gjennomføringssted

5. **Sensorensdel** - Sensurering av kandidatens deler
   - SensorensdelID (PK)
   - KandidatperiodeID (FK), EksamensperiodeID (FK)
   - Gjennomføringsinformasjon, Delkarakter

6. **Eksamensmateriale** - Eksamensmateriale
   - EksamensmaterialeID (PK)
   - EksamensperiodeID (FK)
   - Filnavn

7. **Beverordredel** - Bevervurderingsdeler
   - BeverordredelID (PK)
   - SensorensdelID (FK)
   - BlobReference, PageStatus, IsAnswersheet
   - Saksnummer, SaksType, OCR-relaterte felt

8. **Eksamensmateriellmottak** - Mottak av eksamensmateriale
   - EksamensmateriellemottakID (PK)
   - EksamensmaterialeID (FK)
   - BlobReference, Filnavn

9. **BevervurderingsMetadata** - Metadata for bevervurdering
   - BevervurderingsMetadataID (PK)
   - BeverordredelID (FK)
   - FileSizeByte, Filnavn

## Kjøring av migration

### Forutsetninger:
Sørg for at database connection er konfigurert i `ormconfig.ts` med riktige connection settings.

### Kommandoer:

```bash
# Kjøre migration
npm run migration:run

# Reversere migration (hvis nødvendig)
npm run migration:revert

# Generere ny migration basert på entity-endringer
npm run migration:generate -- MigrationName
```

## Indekser

Migration scriptet oppretter også ytelsesoptimaliserende indekser på:
- Foreign key kolonner
- Søkekolonner som Fagkode, KandidatNummer
- Dato-kolonner for effektiv sortering/filtrering

## Rollback

`down()` metoden fjerner alle tabeller og indekser i riktig rekkefølge for å respektere foreign key constraints.

## Merk

- Alle tabeller har CreatedDate og ModifiedDate for audit tracking
- VARCHAR størrelser er satt basert på forventet datavolum
- Foreign key constraints sikrer referensiell integritet
- Alle PK/FK felter bruker VARCHAR for fleksibilitet med eksterne systemer

## Testing

Etter kjøring av migration, test at:
1. Alle tabeller er opprettet
2. Foreign key constraints fungerer
3. Indekser er opprettet korrekt
4. Rollback fungerer (test i development miljø)
