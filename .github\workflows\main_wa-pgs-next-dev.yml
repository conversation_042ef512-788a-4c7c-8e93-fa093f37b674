name: Build and deploy Node.js app to Azure Web App

on:
  push:
    branches: ["main", "Develop"]
  workflow_dispatch:

env:
  APP_VERSION: ""
  CUSTOMCONNSTR_APPINSIGHTS_CONNECTIONSTRING: ${{ secrets.APPINSIGHTS_CONNECTION_STRING }}

jobs:
  Build:
    runs-on: ubuntu-latest

    steps:
      - name: Set APP_VERSION environment variable
        run: echo "APP_VERSION=$(date +'%Y.%m').${{ github.run_number }}" >> $GITHUB_ENV

      - name: Show environment variables
        run: |
          echo "APP_VERSION: $APP_VERSION"

      - uses: actions/checkout@v4

      - name: Set up Node.js version
        uses: actions/setup-node@v4
        with:
          node-version: "20.x"

      - name: Cache Node.js modules
        uses: actions/cache@v4
        with:
          path: ~/.npm
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: ${{ runner.os }}-node-

      - name: npm install, build, and test
        run: |
          npm install
          npm run build
          cp -r ./.next/static ./.next/standalone/.next/
          cp -r ./public ./.next/standalone/
          cp package.json ./.next/standalone/
          cp package-lock.json ./.next/standalone/

      - name: Zip build artifacts
        run: zip -r build-artifacts.zip ./.next/standalone/


      - name: Upload zipped build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-artifacts
          path: ./build-artifacts.zip

    outputs:
      app_version: ${{ env.APP_VERSION }}

  Deploy:
    needs: Build
    runs-on: ubuntu-latest
    strategy:
      matrix:
        include:
          - environment: "Dev"
            app-name: "wa-pgs-next-dev"
          - environment: "Test"
            app-name: "wa-pgs-next-test"
          - environment: "Qa"
            app-name: "wa-pgs-next-qa"
          - environment: "Prod"
            app-name: "wa-pgs-next-prod"
    env:
      APP_VERSION: ${{ needs.Build.outputs.app_version }}
    environment:
      name: ${{ matrix.environment }}
    steps:
      - name: Download zipped build artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-artifacts
          path: ./

      - name: Unzip build artifacts
        run: unzip build-artifacts.zip

      - name: "Deploy to Azure Web App"
        uses: azure/webapps-deploy@v3
        with:
          app-name: ${{ matrix.app-name }}
          slot-name: "Production"
          publish-profile: ${{ secrets.AZURE_WEBAPP_PUBLISH_PROFILE }}
          package: ./.next/standalone

      - name: Send custom JSON data to Slack workflow
        if: matrix.environment == 'Dev' # Send notification only for Dev
        uses: slackapi/slack-github-action@v1.27.0
        with:
          payload: |
            {
              "text": "Versjon *${{ env.APP_VERSION }}* av *PGS* er rullet ut til *${{ matrix.environment }}*",
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "Versjon *${{ env.APP_VERSION }}* av *PGS* er rullet ut til *${{ matrix.environment }}*"
                  }
                }
              ]
            }
        env:
          SLACK_WEBHOOK_URL: "*******************************************************************************"
          SLACK_WEBHOOK_TYPE: INCOMING_WEBHOOK
