import { Metadata } from "next";
import { getTranslations } from "next-intl/server";

export const metadata: Metadata = {
  title: "PGS - Fravær",
  description: "Fravær",
};

const Fravaer = async ({}) => {
  //const userSessionData = await getUserSessionData();
  //const t = await getTranslations("Fravaer");

  const t = await getTranslations("Fravaer");
 // await checkAccessAndNavigate(RouteEnum.Fravaer, userSessionData);
  return (
    <div className="mx-auto my-28 flex flex-col gap-6">
      <h1 className="text-5xl font-bold">{t("AbsenceHeading")}</h1>
      <div className="flex flex-col gap-3 w-full lg:w-2/3 p-8 bg-white rounded">
        <p>{t("AbsenceRegistered")}</p>
        <p>{t("TryAgain")}</p>
      </div>
    </div>
  );
};

export default Fravaer;
