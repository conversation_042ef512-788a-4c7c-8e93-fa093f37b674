import { generateSignalRAccessToken } from "./generateSignalRToken";
import { SignalRMessageEnum } from "../enums/SignalRMessageEnum";

const hubName = process.env.AZURE_SIGNALR_HUB_NAME || "pgshub";
const connectionString =
  process.env.CUSTOMCONNSTR_AZURE_SIGNALR_CONNECTION_STRING || "";
const endpoint = connectionString?.match(/Endpoint=(.*?);/)?.[1] ?? "";

export async function sendSignalRStatusRequest(
  schoolId: string,
  candidateNumber: string,
  isAccessRequest: boolean,
  sessionId: string
): Promise<string> {
  try {
    const url = `${endpoint}/api/v1/hubs/${hubName}/groups/${schoolId}`;
    // Send melding til SignalR gruppen

    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${generateSignalRAccessToken({
          audience: url,
          lifetime: 60,
        })}`,
      },
      body: JSON.stringify({
        target: SignalRMessageEnum.AccessRequest,
        arguments: [
          {
            id: sessionId,
            candidateNumber: candidateNumber,
            hasRequestedAccess: isAccessRequest,
            schoolId: schoolId,
          },
        ],
      }),
    });

    if (!response.ok) {
      throw new Error("Failed to send access request");
    }

    return sessionId;
  } catch (error) {
    console.error("Error:", error);
    if (error instanceof Error) {
    }
    throw error;
  }
}
