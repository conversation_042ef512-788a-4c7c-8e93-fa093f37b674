import { IUserSessionData } from "../interfaces/IUserSessionData";
import { getClientIp } from "./getClientIp";
import { getPgsaAccessToken } from "./getPgsaAccesstoken";
import { InitializeCandidateError } from "./exceptionTypes";
import { IStatusInfo } from "../interfaces/IStatusInfo";
import { getAppInsightsServer } from "./appInsightsServer";

const PgsaApiUrl = process.env.PGSA_API_ASYNC_URL;
const apiUrl = `${PgsaApiUrl}/api/candidate/initialize`;
const telemetryClient = getAppInsightsServer();

export async function initializeCandidate(
  userInfoFromRedis: IUserSessionData
): Promise<IStatusInfo | null> {
  if (!PgsaApiUrl) {
    throw new Error("PGSA_API_ASYNC_URL is not set in environment variables.");
  }

  const [pgsaAccessToken, clientIp] = await Promise.all([
    getPgsaAccessToken(),
    getClientIp(),
  ]);

  if (!pgsaAccessToken) {
    throw new Error("Failed to obtain PGSA access token.");
  }

  const requestBody = {
    CandidateNumber: userInfoFromRedis.candidateNumber,
    DayCode: userInfoFromRedis.dayCode,
    ExamGroupCode: userInfoFromRedis.candidateGroupCode,
    Language: userInfoFromRedis.language,
    UserId: userInfoFromRedis.userId,
    IpAddress: clientIp,
  };

  try {
    const response = await fetch(apiUrl, {
      method: "POST",
      body: JSON.stringify(requestBody),
      headers: {
        Authorization: `Bearer ${pgsaAccessToken}`,
        "Content-Type": "application/json",
      },
    });

    // Sjekk responsen før parsing
    if (!response.ok) {
      const error = new InitializeCandidateError(
        `Feil ved initialisering av bruker i PGSA. Statuskode: ${response.status}. Respons: ${response.statusText}`
      );

      throw error;
    }

    // Sjekk for korrekt innholdstype før parsing
    const contentType = response.headers.get("content-type");
    if (!contentType || !contentType.includes("application/json")) {
      throw new TypeError("Mottok ikke JSON som forventet");
    }

    const responseObject = await response.json();

    if (!responseObject || typeof responseObject !== "object") {
      throw new InitializeCandidateError("Ugyldig respons format fra PGSA");
    }

    return responseObject.data;
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";

    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        component: "initializeCandidate",
        action: "generalCatchBlock",
        requestBody: JSON.stringify(requestBody),
        errorMessage: errorMessage,
      },
    });

    console.error("Error in initializeCandidate:", errorMessage);
    throw error;
  } finally {
  }
}
