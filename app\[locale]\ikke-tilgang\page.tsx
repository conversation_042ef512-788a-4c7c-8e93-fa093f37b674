import { Metadata } from "next";
import { getTranslations } from "next-intl/server";

export const metadata: Metadata = {
  title: "PGS - Blokkert",
  description: "Blokkert side for PGS",
};

const IkkeTilgang = async ({}) => {
  const t = await getTranslations("IkkeTilgang");

  // await checkAccessAndNavigate(RouteEnum.Papir, userSessionData);

  return (
    <div className="mx-auto my-28 flex flex-col gap-6">
      <h1 className="text-5xl font-bold">{t("Header")}</h1>
      <div className="flex flex-col gap-3 w-fit p-8 bg-white rounded">
        <p>{t("Message")}</p>
      </div>
    </div>
  );
};

export default IkkeTilgang;
