"use client";

import React, { useEffect } from "react";
import { createSharedPathnamesNavigation } from "next-intl/navigation";
import { locales } from "@/middleware";
import { IPgsSignalRMessage } from "@/app/interfaces/IPgsSignalRMessage";
import { useSignalR } from "@/app/context/signalRContext";
import { SignalRMessageEnum } from "@/app/enums/SignalRMessageEnum";

export const { Link, redirect, usePathname, useRouter } =
  createSharedPathnamesNavigation({ locales });

function OperationalMessage() {
  const [messages, setMessages] = React.useState<IPgsSignalRMessage[]>([]);
  const { connection } = useSignalR();

  useEffect(() => {
    if (connection) {
      connection.on(
        SignalRMessageEnum.MessageRecieve,
        (id, message, audience, audienceId) => {
          setMessages((prevMessages) => [
            ...prevMessages,
            { id, message, audience, audienceId },
          ]);
        }
      );

      connection.on(SignalRMessageEnum.MessageDelete, (id) => {
        setMessages((prevMessages) => prevMessages.filter((m) => m.id !== id));
      });
    }
  }, [connection]);

  useEffect(() => {
    (async () => {
      const response = await fetch("/api/signalRMessages", {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (response.ok) {
        const data = await response.json();
        setMessages(data.messages);
      }
    })();
  }, []);

  return (
    <div>
      {messages.map((message) => (
        <div
          key={message.id}
          className="text-center mb-px w-full text-white bg-red-500 py-1"
        >
          {message.message}
        </div>
      ))}
    </div>
  );
}

export default OperationalMessage;
