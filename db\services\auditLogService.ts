import { IAuditLogData } from "@/app/interfaces/IAuditLogData";
import { OperationEnum } from "@/app/enums/OperationEnum";

export class AuditLogService {
  private static instance: AuditLogService;

  public static getInstance(): AuditLogService {
    if (!AuditLogService.instance) {
      AuditLogService.instance = new AuditLogService();
    }
    return AuditLogService.instance;
  }

  /**
   * Format operation description by replacing template placeholders with actual values
   */
  private formatOperationDescription(
    template: string,
    parameters: Record<string, any> | undefined
  ): string {
    if (!parameters) {
      return template;
    }

    let formatted = template;
    Object.keys(parameters).forEach((key) => {
      const placeholder = `{${key}}`;
      formatted = formatted.replace(
        new RegExp(placeholder, "g"),
        parameters[key].toString()
      );
    });

    return formatted;
  }

  /**
   * Build audit log data with template-based description - High performance (no database access)
   */
  public buildAuditLogData(
    baseData: Omit<IAuditLogData, "operationId" | "parameters">,
    operationId: number,
    parameters?: Record<string, any>
  ): IAuditLogData | null {
    try {
      const template = OperationEnum[operationId];
      if (!template) {
        console.error(`Template not found for operation ID: ${operationId}`);
        return null;
      }

      return {
        ...baseData,
        operationId,
        parameters,
      };
    } catch (error) {
      console.error("Failed to build audit log data:", error);
      return null;
    }
  }
}

export default AuditLogService;
