"use server";

import { getTranslations } from "next-intl/server";
import { CandidateStatusEnum } from "../enums/CandidateStatusEnum";
import { IUserSessionData } from "../interfaces/IUserSessionData";
import { getStatusInfoFromPgsa } from "./getStatusInfoFromPgsa";
import { getUserSessionData } from "./getUserSessionData";
import { updateCandidateStatus } from "./updateCandidateStatus";
import { StatusInfoEmptyError } from "./exceptionTypes";
import { getAppInsightsServer } from "./appInsightsServer";
import { sendSignalRStatusRequest } from "./sendSignalRStatusRequest";
import { getClientIp } from "./getClientIp";
import {
  deleteHashFieldFromRedis,
  getHashFromRedis,
  setHashInRedis,
} from "./redisHelper";
import AuditLogService from "@/db/services/auditLogService";
import { getUserLoggingInfo } from "./getUserLoggingInfo";
import { OperationEnum } from "../enums/OperationEnum";
import { headers } from "next/headers";
import { sendAuditLogSafe } from "./serviceBusClient";

const telemetryClient = getAppInsightsServer();

export async function validatePassword(prevState: any, formData: FormData) {
  const passwordFromUser = formData.get("password");
  const t = await getTranslations("Autorisering");
  const userSessionData: IUserSessionData = await getUserSessionData();

  try {
    if (!passwordFromUser) {
      return {
        validated: false,
        errorMessage: t("FieldEmpty"),
      };
    }

    const userStatus = await getStatusInfoFromPgsa(userSessionData.userId);
    if (!userStatus) throw new StatusInfoEmptyError();

    const userKey = `candidate:${userSessionData.candidateNumber}:${userSessionData.userSessionId}`;
    const dayCode = await getHashFromRedis(userKey, "dayCode");

    const validated = dayCode === passwordFromUser;

    if (validated) {
      await setHashInRedis(userKey, "isAuthorized", "true");

      telemetryClient?.trackEvent({
        name: "dayCodeValidated",
        properties: {
          userGuid: userSessionData.userSessionId,
          candidateNumber: userSessionData.candidateNumber,
        },
      });

      if (
        userStatus.Status ===
        CandidateStatusEnum.VenterPaaDagspassordIkkeAutentisert
      ) {
        await updateCandidateStatus(
          userStatus.Status,
          CandidateStatusEnum.InnloggetAutentisert,
          userStatus.TestPartId,
          userStatus.CandidateNumber,
          userStatus.ExamGroupCode,
          userStatus.CandidateNumber,
          userSessionData.userId,
          await getClientIp()
        );
      }

      try {
        await sendSignalRStatusRequest(
          userStatus.SchoolId,
          userStatus.CandidateNumber,
          false,
          userSessionData.userSessionId
        );

        const key = `AccessRequest:${userStatus.SchoolId}`;

        // Delete specific candidate's request
        if (userStatus.CandidateNumber) {
          await deleteHashFieldFromRedis(key, userStatus.CandidateNumber);
        }
      } catch (error) {
        // Silent catch as in original code
      }

      const data = await getUserLoggingInfo(
        OperationEnum.AutorisertMedDagspassord
      );

      if (data) {
        // Send to Service Bus synchronously with improved error handling
        const headersList = await headers();
        const userAgent = headersList.get("user-agent") || "Unknown User Agent";
        const success = await sendAuditLogSafe(data, userAgent, {
          blocking: true,
          fallback: true,
          context: "validatePassword"
        });

        if (!success) {
          console.warn("Password validation audit log failed but stored for retry");
        }
      } else {
        console.error("Failed to build audit log data for password validation");
      }
    }

    return {
      validated: validated,
      errorMessage: validated ? "" : t("WrongPassword"),
    };
  } catch (error) {
    console.error("Error:", error);

    return {
      validated: false,
      errorMessage: t("Error"),
    };
  }
}
