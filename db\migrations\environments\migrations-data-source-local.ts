import "reflect-metadata";
import { DataSource } from "typeorm";

const AppDataSource = new DataSource({
  type: "mssql", // SQL Server
  host: process.env.DATABASE_HOST,
  port: 1433,
  username: process.env.DATABASE_USERNAME,
  password: process.env.DATABASE_PASSWORD,
  database: process.env.DATABASE_NAME,
  synchronize: false,
  logging: true, // Enable logging for local development
  entities: [],
  migrations: ["db/migrations/*.ts"],
  migrationsTableName: "migrations",
  subscribers: [],  
  options: {
    encrypt: true,
    enableArithAbort: true,
    trustServerCertificate: true, // For local development
  },
  extra: {
    validateConnection: false,
    trustServerCertificate: true, // For local development
  },
  connectionTimeout: 30000,
  requestTimeout: 30000,
  migrationsRun: false,
  migrationsTransactionMode: "all",
});

export default AppDataSource;
