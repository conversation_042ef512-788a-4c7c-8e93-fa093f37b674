import { Metadata } from "next";
import { ReactNode } from "react";

export const metadata: Metadata = {
  title: "PGS - Kvittering",
  description: "Last ned kvittering",
};

export default async function RootLayout({
  children,
}: {
  children: ReactNode;
}) {
 // const userSessionData = await getUserSessionData();
 // await checkAccessAndNavigate(RouteEnum.Kvittering, userSessionData);

  return (
    <>
    <div id="ExamReceipt">
      {children}
      </div>
    </>
  );
}
