import { Metadata } from "next";
import { getTranslations } from "next-intl/server";
import ReceiptInfo from "./components/ReceiptInfo";
import SubmittedFiles from "./components/SubmittedFiles";
import DownloadReceipt from "./components/DownloadReceipt";

export const metadata: Metadata = {
  title: "PGS - Kvittering",
  description: "Last ned kvittering",
};

const Kvittering = async ({}) => {
  const t = await getTranslations("Kvittering");
  return (
    <>
      <div className="flex flex-col gap-6 mb-8">
        <h1 className="text-5xl font-bold">{t("ReceiptDeliveredExam")}</h1>
      </div>
      <div className="flex flex-col w-full gap-8">
        <div key="receiptInfo">
          <ReceiptInfo />
        </div>
        <div key="submittedFiles">
          <SubmittedFiles />
        </div>
        <div key="downloadReciept">
          <DownloadReceipt />
        </div>
      </div>
    </>
  );
};

export default Kvittering;
