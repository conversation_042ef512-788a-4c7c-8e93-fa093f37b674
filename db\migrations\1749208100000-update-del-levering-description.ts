import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateDelLeveringDescription1749208100000
  implements MigrationInterface
{
  name = "UpdateDelLeveringDescription1749208100000";

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Update operation 114 description
    // Change "Del {partNumber} ble åpnet for ny levering." to "{partNumber} ble åpnet for ny levering."
    await queryRunner.query(`
            UPDATE "Operation"
            SET "BeskrivelseMal" = '{partNumber} ble åpnet for ny levering.'
            WHERE "OperationID" = 114;
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Revert operation 114 description
    await queryRunner.query(`
            UPDATE "Operation"
            SET "BeskrivelseMal" = 'Del {partNumber} ble åpnet for ny levering.'
            WHERE "OperationID" = 114;
        `);
  }
}
