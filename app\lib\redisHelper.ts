import Redis from "ioredis";
import { IUserSessionData } from "../interfaces/IUserSessionData";
import {
  UserInfoRedisError,
  UserPgsaStatusInfoRedisError,
} from "./exceptionTypes";
import { IStatusInfo } from "../interfaces/IStatusInfo";
import { getAppInsightsServer } from "./appInsightsServer";
import { getClientIp } from "./getClientIp";
import dayjs from "dayjs";

interface RedisKeyValue {
  key: string;
  value: string | null;
}

// Initialize Redis client with configuration
const redisClient = new Redis(
  process.env.CUSTOMCONNSTR_PGS_REDIS_CONNECTIONSTRING || "",
  {
    retryStrategy(times) {
      const delay = Math.min(times * 50, 2000);
      return delay;
    },
    maxRetriesPerRequest: 3,
    enableReadyCheck: true,
  }
);

const telemetryClient = getAppInsightsServer();

// Setup error handling for Redis client
redisClient.on("error", (error) => {
  console.error("Redis Client Error:", error);
  telemetryClient?.trackException({
    exception: error,
    properties: {
      component: "RedisHelper",
      action: "connection",
    },
  });
});

export async function saveUserInfoToRedis(
  user: IUserSessionData
): Promise<void> {
  const key = `candidate:${user.candidateNumber}:${user.userSessionId}`;

  try {
    let timeForSession: string | undefined;
    let ipAddress: string | undefined;
    let activeIp: string | undefined;

    // Check if the hash exists already
    const exists = await redisClient.exists(key);

    if (exists) {
      // Get existing values for timeForSession and ipAddress
      const existingData = await redisClient.hmget(
        key,
        "timeForSession",
        "ipAddress"
      );
      timeForSession = existingData[0] || undefined;
      ipAddress = existingData[1] || undefined;
      activeIp = existingData[1] || undefined;
    } else {
      //    const ip = await getClientIp();
      timeForSession = dayjs().utc().toISOString();
      ipAddress = "";
      activeIp = "";
      // activeIp = "";
    }

    // Prepare hash fields
    const newUserObj = {
      ...user,
      timeForSession,
      ipAddress,
      activeIp,
    };

    // Convert object fields to string values for Redis hash
    const hashFields: Record<string, string> = {};
    for (const [key, value] of Object.entries(newUserObj)) {
      if (value !== undefined) {
        hashFields[key] =
          typeof value === "object" ? JSON.stringify(value) : String(value);
      }
    }

    // Set all hash fields at once
    await redisClient.hmset(key, hashFields);

    // Set expiration to end of day
    await redisClient.expireat(key, dayjs().endOf("day").unix());

    // Add session ID to candidateSessions set for quick lookup
    const sessionSetKey = `candidateSessions:${user.candidateNumber}`;
    await redisClient.sadd(sessionSetKey, user.userSessionId);
    await redisClient.expireat(sessionSetKey, dayjs().endOf("day").unix());
  } catch (error) {
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        component: "RedisHelper",
        action: "saveUserInfoToRedis",
        candidateNumber: user.candidateNumber,
        userGuid: user.userSessionId,
        key,
        errorMessage: error instanceof Error ? error.message : String(error),
        errorStack: error instanceof Error ? error.stack : undefined,
      },
    });
    throw error;
  }
}

export async function getUserInfoFromRedis(
  userGuid: string,
  candidateNumber: string
): Promise<IUserSessionData> {
  if (!userGuid || !candidateNumber) {
    throw new Error("UserGuid or candidateNumber is missing");
  }

  const key = `candidate:${candidateNumber}:${userGuid}`;

  try {
    // Get all hash fields
    const hashData = await redisClient.hgetall(key);

    if (!hashData || Object.keys(hashData).length === 0) {
      throw new UserInfoRedisError();
    }

    // Define known JSON fields for faster parsing decision
    const jsonFields = new Set(["isAuthorized", "isNewAuthentication"]);

    // Use a temporary Record to avoid TypeScript errors during construction
    const tempData: Record<string, any> = {};

    for (const [field, value] of Object.entries(hashData)) {
      // Only try parsing JSON for fields we know should be JSON
      if (jsonFields.has(field)) {
        try {
          tempData[field] = JSON.parse(value);
        } catch {
          // Use as string if parsing fails
          tempData[field] = value;
        }
      } else {
        // Use string value directly for non-JSON fields
        tempData[field] = value;
      }
    }

    return tempData as IUserSessionData;
  } catch (error) {
    console.error("Error in getUserInfoFromRedis:", error);
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        component: "RedisHelper",
        action: "getUserInfoFromRedis",
        userGuid,
        candidateNumber,
        key,
      },
    });
    throw error;
  }
}

export async function setValueInRedis(
  key: string,
  value: string,
  expirationInSeconds: number
) {
  try {
    await redisClient.set(key, value, "EX", expirationInSeconds);
  } catch (error) {
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        component: "RedisHelper",
        action: "setValueInRedis",
        key,
      },
    });
    throw error;
  }
}

export async function getValueFromRedis(key: string): Promise<string | null> {
  try {
    return await redisClient.get(key);
  } catch (error) {
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        component: "RedisHelper",
        action: "getValueFromRedis",
        key,
      },
    });
    throw error;
  }
}

export async function getPgsaStatusInfoFromRedis(
  userId: string
): Promise<IStatusInfo | null> {
  if (!userId) {
    throw new Error("UserId is missing");
  }

  const key = `CandidateInfoStatusCache_CandidateInfoStatusCache:${userId}`;
  try {
    const value = await redisClient.get(key);
    if (!value) {
      return null;
    }
    return JSON.parse(value);
  } catch (error) {
    console.error("Error in getPgsaStatusInfoFromRedis:", error);
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        component: "RedisHelper",
        action: "getPgsaStatusInfoFromRedis",
        userId,
        key,
      },
    });
    throw new UserPgsaStatusInfoRedisError();
  }
}

export async function getContentWithPrefix(
  prefix: string
): Promise<RedisKeyValue[]> {
  const results: RedisKeyValue[] = [];
  let cursor = "0";

  try {
    do {
      const [nextCursor, matchedKeys] = await redisClient.scan(
        cursor,
        "MATCH",
        `${prefix}*`,
        "COUNT",
        1000
      );

      cursor = nextCursor;

      if (matchedKeys.length > 0) {
        // First check key types
        const typePipeline = redisClient.pipeline();
        matchedKeys.forEach((key) => typePipeline.type(key));
        const typeResults = await typePipeline.exec();

        // Process each key based on its type
        for (let i = 0; i < matchedKeys.length; i++) {
          const key = matchedKeys[i];
          const [typeErr, keyType] = typeResults?.[i] || [null, null];

          if (typeErr) {
            console.error(`Error checking type for key ${key}:`, typeErr);
            continue;
          }

          try {
            if (keyType === "hash") {
              // For hash type, get all fields
              const hashData = await redisClient.hgetall(key);
              results.push({
                key,
                value: JSON.stringify(hashData),
              });
            } else if (keyType === "string") {
              // For string type, get the value directly
              const value = await redisClient.get(key);
              results.push({
                key,
                value,
              });
            }
          } catch (err) {
            console.error(`Error processing key ${key}:`, err);
          }
        }
      }
    } while (cursor !== "0");

    return results;
  } catch (error) {
    console.error("Error in getContentWithPrefix", error);
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        component: "RedisHelper",
        action: "getContentWithPrefix",
        prefix,
      },
    });
    return [];
  }
}

// Hash-funksjoner
export async function setHashInRedis(
  key: string,
  field: string,
  value: string,
  expirationInSeconds?: number
) {
  await redisClient.hset(key, field, value);
  if (expirationInSeconds) {
    await redisClient.expire(key, expirationInSeconds);
  } else {
    await redisClient.expireat(key, dayjs().endOf("day").unix());
  }
}

export async function isSetMember(
  key: string,
  member: string
): Promise<boolean> {
  if (!key || !member) {
    throw new Error("Key or member is missing");
  }

  try {
    const isMember = await redisClient.sismember(key, member);
    return isMember === 1;
  } catch (error) {
    throw error;
  }
}

export async function getHashFromRedis(key: string, field: string) {
  return await redisClient.hget(key, field);
}

export async function getAllHashFromRedis(key: string) {
  return await redisClient.hgetall(key);
}

export async function deleteHashFieldFromRedis(key: string, field: string) {
  return await redisClient.hdel(key, field);
}

export async function getHashFieldsFromRedis(key: string) {
  return await redisClient.hkeys(key);
}

// Funksjon for å sjekke om en hash-nøkkel eksisterer
export async function hashKeyExists(key: string) {
  return await redisClient.exists(key);
}

// Funksjon for å sette flere hash-felter samtidig
export async function setMultipleHashFields<T extends Record<string, any>>(
  key: string,
  fieldValues: T,
  expirationInSeconds?: number
) {
  if (!key || typeof key !== "string") {
    throw new Error("Invalid key");
  }

  const entries = Object.entries(fieldValues).map(([field, value]) => [
    field,
    typeof value === "object" ? JSON.stringify(value) : String(value),
  ]);

  await redisClient.hmset(key, Object.fromEntries(entries));

  if (expirationInSeconds) {
    await redisClient.expire(key, expirationInSeconds);
  } else {
    await redisClient.expireat(key, dayjs().endOf("day").unix()); // Sett til midnatt
  }
}

export async function getMultipleHashFields(key: string, fields: string[]) {
  return await redisClient.hmget(key, ...fields);
}

// Hent alle sesjons-IDer for en kandidat
export async function getSessionIdsForCandidate(candidateNumber: string): Promise<string[]> {
  const sessionSetKey = `candidateSessions:${candidateNumber}`;
  return await redisClient.smembers(sessionSetKey);
}

// Slett set med alle kandidatens sesjoner
export async function deleteCandidateSessionSet(candidateNumber: string): Promise<void> {
  const sessionSetKey = `candidateSessions:${candidateNumber}`;
  await redisClient.del(sessionSetKey);
}
