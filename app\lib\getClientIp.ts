"use server";

import { headers } from "next/headers";

// Enkel regex for å sjekke om adressen er en gyldig IPv4 eller IPv6
const ipv4Pattern = /^(\d{1,3}\.){3}\d{1,3}$/;
const ipv6Pattern = /^([0-9a-fA-F]{0,4}:){1,7}[0-9a-fA-F]{0,4}$/;

export async function getClientIp(): Promise<string> {
  try {
    const header = await headers();
    const forwarded = header.get("x-forwarded-for") ?? "";
    let firstValidIPv6 = ""; // To store the first valid IPv6 found

    if (forwarded) {
      const addresses = forwarded.split(",");

      // First pass: Look for the first valid IPv4 address
      for (const address of addresses) {
        const trimmedAddress = address.trim();
        if (ipv4Pattern.test(trimmedAddress)) {
          return trimmedAddress; // Return the first valid IPv4 found
        }
        // Keep track of the first valid IPv6 in case no IPv4 is found
        if (!firstValidIPv6 && ipv6Pattern.test(trimmedAddress)) {
          firstValidIPv6 = trimmedAddress;
        }
      }
    }

    // If no IPv4 was found, return the first valid IPv6 (if any)
    return firstValidIPv6; // Returns "" if no valid IP was found
  } catch (error) {
    console.error("Error in getClientIp:", error);
    throw error;
  }
}
