/** @type {import('next').NextConfig} */

const withNextIntl = require("next-intl/plugin")(
  // This is the default (also the `src` folder is supported out of the box)
  "./i18n.ts"
);

const nextConfig = {
  // Other Next.js configuration ...
  reactStrictMode: false,
  compress: false,
  output: "standalone",
  webpack: (config) => {
    config.resolve.alias.canvas = false;
    return config;
  },
  experimental: {
  },
  env: {
    //APP_VERSION: process.env.APP_VERSION,
  },
};

module.exports = withNextIntl(nextConfig);
