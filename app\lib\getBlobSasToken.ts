export const getBlobSasToken = async (
  blobName: string,
  containerName: string
): Promise<string> => {
  try {
    // Using URLSearchParams to properly encode parameters
    const params = new URLSearchParams({
      container: containerName,
      // Only include blobname if we need blob-specific access
      ...(blobName && { blobname: blobName }),
    });

    const response = await fetch(
      `${window.location.origin}/api/getblobsastoken?${params}`,
      { cache: "no-store" }
    );

    if (!response.ok) {
      throw new Error(
        `Klarte ikke å hente sastoken. Feilkode:${response.status}. Feilmelding: ${response.statusText}`
      );
    }

    const data = await response.json();

    // If we have a specific blob, append it to the container URL
    if (blobName && !data.sastoken.includes(blobName)) {
      const [baseUrl, queryString] = data.sastoken.split("?");
      return `${baseUrl}/${blobName}?${queryString}`;
    }

    return data.sastoken;
  } catch (error) {
    console.error("Klarte ikke å hente sastoken:", error);
    throw new Error("Klarte ikke å hente sastoken");
  }
};
