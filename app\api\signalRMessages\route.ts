import { Audience } from "@/app/enums/Audience";
import { SignalRMessageEnum } from "@/app/enums/SignalRMessageEnum";
import { IPgsSignalRMessage } from "@/app/interfaces/IPgsSignalRMessage";
import { generateSignalRAccessToken } from "@/app/lib/generateSignalRToken";
import { getUserSessionData } from "@/app/lib/getUserSessionData";
import { getValueFromRedis, setValueInRedis } from "@/app/lib/redisHelper";
import { NextResponse } from "next/server";

const connectionString =
  process.env.CUSTOMCONNSTR_AZURE_SIGNALR_CONNECTION_STRING || "";
const hubName = process.env.AZURE_SIGNALR_HUB_NAME || "pgshub";
const endpoint = connectionString?.match(/Endpoint=(.*?);/)?.[1] ?? "";
const redisKey = "PGS:Signalr:Messages";

export async function GET(request: Request) {
  try {
    const userSessionData = await getUserSessionData();

    if (!userSessionData?.userSessionId)
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });

    let redisData: IPgsSignalRMessage[] = [];

    const redisDataString = await getValueFromRedis(redisKey);

    if (redisDataString) {
      redisData = JSON.parse(redisDataString);
      redisData = redisData.filter(
        (m) =>
          m.audience === Audience.All ||
          (m.audience === Audience.User &&
            m.audienceId === userSessionData.candidateNumber) ||
          (m.audience === Audience.Group &&
            m.audienceId === userSessionData.candidateGroupCode)
      );
    }

    return NextResponse.json({ messages: redisData });
  } catch (error) {
    return NextResponse.json(
      { error: "Feil under henting av meldinger" },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  const userSessionData = await getUserSessionData();

  if (!userSessionData?.userSessionId)
    return NextResponse.json({ message: "Unauthorized" }, { status: 401 });

  const messageToSend: IPgsSignalRMessage = await request.json();

  if (!messageToSend?.message || !messageToSend?.audience || !messageToSend?.id)
    return NextResponse.json(
      { message: "Missing id, message, audience or audienceId" },
      { status: 400 }
    );

  let url = "";

  switch (messageToSend.audience) {
    case Audience.All:
      url = `${endpoint}/api/v1/hubs/${hubName}`;
      break;
    case Audience.User:
      url = `${endpoint}/api/v1/hubs/${hubName}/users/${messageToSend.audienceId}`;
      break;
    default:
      url = `${endpoint}/api/v1/hubs/${hubName}/groups/${messageToSend.audienceId}`;
      break;
  }

  try {
    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${generateSignalRAccessToken({
          audience: url,
          lifetime: 60,
        })}`,
      },
      body: JSON.stringify({
        target: SignalRMessageEnum.MessageRecieve,
        arguments: [messageToSend.id, messageToSend.message],
      }),
    });

    let redisData: IPgsSignalRMessage[] = [];

    const redisDataString = await getValueFromRedis(redisKey);

    if (redisDataString) {
      redisData = JSON.parse(redisDataString);
    }

    redisData.push({
      id: messageToSend.id,
      audience: messageToSend.audience,
      audienceId: messageToSend.audienceId,
      message: messageToSend.message,
    });

    await setValueInRedis(redisKey, JSON.stringify(redisData), 3600);

    return NextResponse.json({ message: "Message sent" }, { status: 200 });
  } catch (error) {
    console.error("Error sending message:", error);

    return NextResponse.json(
      { message: "Error sending message" },
      { status: 500 }
    );
  }
}

//add new delete method
export async function DELETE(request: Request) {
  const userSessionData = await getUserSessionData();

  if (!userSessionData?.userSessionId)
    return NextResponse.json({ message: "Unauthorized" }, { status: 401 });

  const messageToRemove = await request.json();

  const url = `${endpoint}/api/v1/hubs/${hubName}`;

  if (!messageToRemove?.id)
    return NextResponse.json(
      { message: "No message id provided" },
      { status: 400 }
    );

  try {
    let redisData: IPgsSignalRMessage[] = [];

    const redisDataString = await getValueFromRedis(redisKey);

    if (redisDataString) {
      redisData = JSON.parse(redisDataString);
    }

    redisData = redisData.filter((m) => m.id !== messageToRemove.id);

    await setValueInRedis(redisKey, JSON.stringify(redisData), 3600);

    await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${generateSignalRAccessToken({
          audience: url,
          lifetime: 60,
        })}`,
      },
      body: JSON.stringify({
        target: SignalRMessageEnum.MessageDelete,
        arguments: [messageToRemove.id],
      }),
    });

    return NextResponse.json({ message: "Message deleted" }, { status: 200 });
  } catch (error) {
    console.error("Error deleting message:", error);

    return NextResponse.json(
      { message: "Error deleting message" },
      { status: 500 }
    );
  }
}
