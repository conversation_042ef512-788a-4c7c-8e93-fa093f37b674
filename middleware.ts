import { NextRequest, NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import createIntlMiddleware from "next-intl/middleware";
import { withAuth } from "next-auth/middleware";
import { ValidLocale } from "@/app/interfaces/ILocale";

const COOKIE_NAME = process.env.PGS_USER_COOKIE_NAME || "";
export const locales: ValidLocale[] = ["nb", "nn"];

interface TokenUser {
  userSessionId: string;
  candidateNumber: string;
  userId: string;
}

const intlMiddleware = createIntlMiddleware({
  locales,
  defaultLocale: "nb",
  localePrefix: "never",
});

async function validateSession(
  req: NextRequest,
  token: any
): Promise<NextResponse | null> {
  if (!token) {
    return NextResponse.redirect(new URL("/api/auth/signin", req.url));
  }

  const validationCookie = req.cookies.get(COOKIE_NAME);
  if (!validationCookie) {
    const redirectUrl = `${process.env.NEXTAUTH_UIDP_URL}/connect/endsession?id_token_hint=${token.idToken}&post_logout_redirect_uri=${process.env.NEXTAUTH_URL}`;

    const response = NextResponse.redirect(new URL(redirectUrl));
    response.cookies.set(
      `${
        process.env.PGS_ENVIRONMENT === "localhost" ? "" : "__Secure-"
      }next-auth.session-token`,
      "",
      {
        httpOnly: true,
        maxAge: -1,
        path: "/",
        secure: process.env.PGS_ENVIRONMENT !== "localhost",
        sameSite: "lax",
      }
    );
    return response;
  }
  return null;
}

async function checkAccess(
  req: NextRequest,
  user: TokenUser,
  clientIp: string
): Promise<NextResponse | null> {
  try {
    const response = await fetch(`${process.env.NEXTAUTH_URL}/api/access`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        currentRoute: req.nextUrl.pathname,
        userSessionId: user.userSessionId,
        candidateNumber: user.candidateNumber,
        userId: user.userId,
        clientIp,
      }),
    });

    if (!response.ok) {
      console.error(
        "Error from /api/access:",
        response.status,
        response.statusText
      );
      return NextResponse.redirect(new URL("/error", req.url)); // Redirect to an error page
    }

    const { redirect } = await response.json();

    if (redirect && redirect !== req.nextUrl.pathname) {
      // Check if the redirect is an internal URL
      if (redirect.startsWith("/")) {
        return NextResponse.redirect(new URL(redirect, req.url));
      } else {
        console.warn("Attempted redirect to external URL:", redirect);
        return NextResponse.redirect(new URL("/error", req.url)); // Redirect to an error page
      }
    }
    return null;
  } catch (error) {
    console.error("Error calling /api/access:", error);
    return NextResponse.redirect(new URL("/error", req.url)); // Redirect to an error page
  }
}

export async function middleware(request: NextRequest) {
  const token = await getToken({
    req: request,
    secret: process.env.NEXTAUTH_SECRET,
  });

  const clientIp = request.headers.get("x-forwarded-for") ?? request.headers.get("x-real-ip") ?? "unknown";

  const sessionValidationResponse = await validateSession(request, token);
  if (sessionValidationResponse) {
    return sessionValidationResponse;
  }

  // Type assertion for token.user
  const user = (token as any).user as TokenUser;

  const accessCheckResponse = await checkAccess(request, user, clientIp ?? "");
  if (accessCheckResponse) {
    return accessCheckResponse;
  }

  // Run intlMiddleware
  const intlResponse = intlMiddleware(request);
  if (intlResponse) {
    return intlResponse;
  }

  // If no redirect, continue
  return NextResponse.next();
}

export const config = {
  matcher: ["/((?!api|_next|.*\\..*).*)"],
};
