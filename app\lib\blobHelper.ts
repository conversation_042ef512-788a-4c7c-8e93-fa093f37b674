import {
  BlobServiceClient,
  generateBlobSASQueryParameters,
  BlobSASPermissions,
  StorageSharedKeyCredential,
} from "@azure/storage-blob";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import { getValueFromRedis, setValueInRedis } from "./redisHelper";

// Initialize dayjs with UTC plugin
dayjs.extend(utc);

export const dynamic = "force-dynamic";

interface Config {
  pgsUrl: string;
  connectionString: string;
}

export interface SasTokenConfig {
  startOffsetMinutes: number;
  expiryMinutes: number;
  cacheDurationMinutes?: number;
}

// Default token configuration
const defaultTokenConfig: SasTokenConfig = {
  startOffsetMinutes: 5,
  expiryMinutes: 15,
};

export class BlobServiceManager {
  // Added export
  private static instance: BlobServiceClient | null = null;
  private static config: Config | null = null;
  private static readonly instanceId = crypto.randomUUID();

  static initialize(): void {
    const pgsUrl = process.env.PGS_URL || "";
    const connectionString =
      process.env.CUSTOMCONNSTR_PGS_BLOB_CONNECTIONSTRING || "";

    this.config = { pgsUrl, connectionString };
  }

  static getInstance(): BlobServiceClient {
    if (!this.config) {
      throw new Error(
        "BlobServiceManager not initialized. Run initialize() first."
      );
    }

    if (!this.instance) {
      this.instance = BlobServiceClient.fromConnectionString(
        this.config.connectionString
      );
    }

    return this.instance;
  }

  static getConfig(): Config {
    if (!this.config) {
      throw new Error(
        "BlobServiceManager not initialized. Run initialize() first."
      );
    }
    return this.config;
  }

  static reset(): void {
    this.instance = null;
    this.config = null;
  }
}

// Initialize at startup
BlobServiceManager.initialize();

/**
 * Generate a Redis key for caching container-level SAS tokens based on permission type.
 */
function generateContainerRedisKey(
  containerName: string,
  permission: string
): string {
  let type = "unknown";
  // Prioritize write/upload key if 'w' is present
  if (permission.includes("w")) {
    type = "upload"; // Covers 'w', 'wc', 'wtr', etc.
  } else if (permission === "rd") {
    type = "read-delete";
  } else if (permission === "r") {
    type = "read";
  } else if (permission === "d") { // Add this condition
    type = "delete";
  }
  // Add more specific types if needed (e.g., 'delete' if 'd' is used alone)
  return `SasToken:container:${type}:${containerName}`;
}

/**
 * Generate a SAS token query string with optional caching for container-level tokens.
 * @param containerName - Azure storage container name
 * @param blobName - Name of the blob (empty string or undefined for container SAS)
 * @param permission - Permission string (e.g. 'r' for read)
 * @param tokenConfig - Optional configuration including cache settings
 * @param useCache - Whether to use Redis caching for container SAS (default: false)
 */
export async function generateSasTokenQueryString(
  containerName: string,
  blobName: string,
  permission: string,
  tokenConfig: Partial<SasTokenConfig> = {},
  useCache = false
): Promise<string> {
  try {
    const finalConfig = { ...defaultTokenConfig, ...tokenConfig };

    // Check cache if enabled (only for container SAS for now)
    let redisKey: string | null = null;
    if (useCache && finalConfig.cacheDurationMinutes) {
      redisKey = generateContainerRedisKey(containerName, permission);
      const cachedToken = await getValueFromRedis(redisKey);

      if (cachedToken) {
        // Return cached query string directly
        return cachedToken;
      }
    }

    const blobServiceClient = BlobServiceManager.getInstance();

    // Verify credential type
    if (!(blobServiceClient.credential instanceof StorageSharedKeyCredential)) {
      throw new Error(
        "BlobServiceClient not initialized with StorageSharedKeyCredential"
      );
    }

    // Generate token with UTC time handling
    const now = dayjs().utc();
    const startsOn = now
      .subtract(finalConfig.startOffsetMinutes, "minute")
      .toDate();
    const expiresOn = now.add(finalConfig.expiryMinutes, "minute").toDate();

    // Build SAS options (omit blobName for container SAS)
    const sasOptions: any = {
      containerName,
      permissions: BlobSASPermissions.parse(permission),
      startsOn,
      expiresOn,
    };
    if (blobName) {
      sasOptions.blobName = blobName;
    }

    const sasQueryString = generateBlobSASQueryParameters(
      sasOptions,
      blobServiceClient.credential
    ).toString();

    // Cache the query string if caching is enabled for container SAS
    if (redisKey && finalConfig.cacheDurationMinutes) {
      const cacheDurationSeconds = finalConfig.cacheDurationMinutes * 60;
      // Store the query string directly
      await setValueInRedis(redisKey, sasQueryString, cacheDurationSeconds);
    }

    // Return only the query string
    return sasQueryString;
  } catch (error) {
    console.error("Error generating SAS token:", error);
    throw new Error("Failed to generate SAS token");
  }
}
