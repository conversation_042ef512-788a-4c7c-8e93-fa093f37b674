export interface IDeliveredFile {
  testPartId: number;
  file?: File | null;
  fileName: string;
  fileGuid: string;
  size: number;
  uploadDate: Date;
  uploadFinished: boolean;
  error?: string;
  isRejected: boolean;
  checked: boolean;
  mimetype: string;
  fileExtension: string;
  downloading: boolean;
  deleting?: boolean;
}

export interface IDeleteFileIOS {
  fileGuid: string;
  isRejected: boolean;
}

export type FileType = IDeliveredFile | IDeleteFileIOS;
