import { AllowedFileTypesError } from "./exceptionTypes";
import { getPgsaAccessToken } from "./getPgsaAccesstoken";
import { getValueFromRedis, setValueInRedis } from "./redisHelper";

const PgsaApiUrl = process.env.PGSA_API_URL;
const redisKey = "PGS:AllowedMimeTypes";

export async function getAllowedMimeTypes(): Promise<IAllowedMimeTypes[]> {
  const redisResponse = await getValueFromRedis(redisKey);

  if (redisResponse) {
    return JSON.parse(redisResponse) as IAllowedMimeTypes[];
  }

  const accessToken = await getPgsaAccessToken();
  const mimeTypesResponse = await fetch(`${PgsaApiUrl}/api/utils/mimetype`, {
    method: "GET",
    headers: {
      Authorization: `Bearer ${accessToken}`,
      "Content-Type": "application/json",
    },
    cache: "no-store",
  });

  if (!mimeTypesResponse.ok) {
    throw new AllowedFileTypesError();
  }

  const fileTypesDataJson: any = await mimeTypesResponse.json();
  const filetypesData: IAllowedMimeTypes[] =
    fileTypesDataJson?.MimeTypeFileExtension ?? [];

  const allowedMimeTypes = filetypesData.filter(
    (mimeType) => mimeType.AllowedInUpload
  );

  await setValueInRedis(redisKey, JSON.stringify(allowedMimeTypes), 1200);

  return allowedMimeTypes;
}
