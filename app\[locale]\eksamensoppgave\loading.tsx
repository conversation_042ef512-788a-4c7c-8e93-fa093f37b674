import { Skeleton } from "@/components/ui/skeleton";
import React from "react";

const Loading = () => {
  return (
    <>
      <div className="flex flex-col gap-6 mb-8">
        <Skeleton className="w-1/2 h-12 bg-base-300" />
        <div className="flex flex-col gap-2">
          <Skeleton className="w-32 h-5 bg-base-300" />
          <Skeleton className="w-48 h-5 bg-base-300" />
        </div>
      </div>
      <div className="flex flex-col lg:w-2/3 gap-8 mb-10">
        <div key="examInfo">
          <div className="flex flex-col gap-6 border w-full h-48 p-8 border-neutral-400">
            <Skeleton className="w-1/3 h-5 bg-base-300" />
            <div className="flex gap-2">
              <Skeleton className="h-5 w-5 rounded-full bg-base-300" />
              <Skeleton className="w-1/3 h-5 bg-base-300" />
            </div>
            <Skeleton className="w-48 h-12 rounded-md bg-base-300" />
          </div>
        </div>
        <div key="examFiles">
          <div className="flex flex-col gap-6 border w-full p-8 border-neutral-400">
            <div className="flex justify-between">
              <Skeleton className="w-1/3 h-5 bg-base-300" />
              <Skeleton className="w-24 h-5 bg-base-300" />
            </div>
            <div className="flex gap-10">
              <Skeleton className="w-48 h-12 rounded-md bg-base-300" />
              <Skeleton className="w-48 h-12 rounded-md bg-base-300" />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Loading;
