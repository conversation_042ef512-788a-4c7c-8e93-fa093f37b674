import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateOperationTypes1749206200000 implements MigrationInterface {
    name = 'UpdateOperationTypes1749206200000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Update existing operations to new types
        await queryRunner.query(`
            UPDATE "Operation"
            SET "Operasjonstype" = CASE "OperationID"
                WHEN 100 THEN 'Autorisering'
                WHEN 101 THEN 'Autorisering'
                WHEN 102 THEN 'Autorisering'
                WHEN 103 THEN 'StatusOppdatering'
                WHEN 104 THEN 'StatusOppdatering'
                WHEN 105 THEN 'StatusOppdatering'
                WHEN 106 THEN 'StatusOppdatering'
                WHEN 107 THEN 'StatusOppdatering'
                WHEN 108 THEN 'StatusOppdatering'
                WHEN 109 THEN 'StatusOppdatering'
                WHEN 110 THEN 'StatusOppdatering'
                WHEN 111 THEN 'Innlevering'
                WHEN 112 THEN 'Innlevering'
                WHEN 113 THEN 'Autorisering'
                WHEN 114 THEN 'Autorisering'
                WHEN 115 THEN 'Autorisering'
                WHEN 116 THEN 'Autorisering'
                WHEN 117 THEN 'StatusOppdatering'
                WHEN 118 THEN 'Sletting'
                WHEN 119 THEN 'Opplasting'
                WHEN 120 THEN 'Innlevering'
                WHEN 121 THEN 'Innlevering'
                WHEN 122 THEN 'Innlevering'
                WHEN 123 THEN 'Nedlasting'
                WHEN 124 THEN 'StatusOppdatering'
                WHEN 125 THEN 'Sjekking'
                WHEN 126 THEN 'Autorisering'
                WHEN 127 THEN 'Opplasting'
                WHEN 128 THEN 'Innlevering'
                WHEN 129 THEN 'Innlevering'
                WHEN 130 THEN 'Innlevering'
                WHEN 131 THEN 'Sletting'
                WHEN 132 THEN 'Sletting'
                WHEN 133 THEN 'Sletting'
                WHEN 134 THEN 'StatusOppdatering'
            END
            WHERE "OperationID" BETWEEN 100 AND 134;
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Restore original operation types
        await queryRunner.query(`
            UPDATE "Operation"
            SET "Operasjonstype" = CASE "OperationID"
                WHEN 100 THEN 'DokumentertFravær'
                WHEN 101 THEN 'TilgangGitt'
                WHEN 102 THEN 'TilgangAvvist'
                WHEN 103 THEN 'UdokumentertFravær'
                WHEN 104 THEN 'FraværOpphevet'
                WHEN 105 THEN 'SendesIPost'
                WHEN 106 THEN 'SendesIPostDel1'
                WHEN 107 THEN 'SendesIPostDel2'
                WHEN 108 THEN 'IkkeSendesIPost'
                WHEN 109 THEN 'IkkeSendesIPostDel1'
                WHEN 110 THEN 'IkkeSendesIPostDel2'
                WHEN 111 THEN 'LevertDigitalt'
                WHEN 112 THEN 'LevertDigitaltDel2'
                WHEN 113 THEN 'ÅpnetForNyLeveringEksamen'
                WHEN 114 THEN 'ÅpnetForNyLevering'
                WHEN 115 THEN 'Sperret'
                WHEN 116 THEN 'SperreOpphevet'
                WHEN 117 THEN 'StatusOppdatert'
                WHEN 118 THEN 'FilSlettet'
                WHEN 119 THEN 'FilLastetOpp'
                WHEN 120 THEN 'FilInnlevert'
                WHEN 121 THEN 'FilInnlevertDel1'
                WHEN 122 THEN 'FilInnlevertDel2'
                WHEN 123 THEN 'FilLastetNed'
                WHEN 124 THEN 'EksamensdelEndret'
                WHEN 125 THEN 'FilSjekket'
                WHEN 126 THEN 'TilgangFjernet'
                WHEN 127 THEN 'FilLastetOppGruppe'
                WHEN 128 THEN 'FilInnlevertGruppe'
                WHEN 129 THEN 'FilInnlevertGruppeDel1'
                WHEN 130 THEN 'FilInnlevertGruppeDel2'
                WHEN 131 THEN 'FilSlettetGruppe'
                WHEN 132 THEN 'FilSlettetGruppeDel1'
                WHEN 133 THEN 'FilSlettetGruppeDel2'
                WHEN 134 THEN 'EksamensdelEndretGruppe'
            END
            WHERE "OperationID" BETWEEN 100 AND 134;
        `);
    }
}