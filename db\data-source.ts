import "reflect-metadata";
import { DataSource } from "typeorm";
import { AuditLog } from "./models/auditLog";
import { Operation } from "./models/operation";
import { DefaultAzureCredential } from "@azure/identity"; // to get the token

const isLocalhost = process.env.PGS_ENVIRONMENT === "localhost";

const AppDataSource = isLocalhost
  ? new DataSource({
      type: "mssql",
      host: process.env.DATABASE_HOST,
      port: 1433,
      username: process.env.DATABASE_USERNAME,
      password: process.env.DATABASE_PASSWORD,
      database: process.env.DATABASE_NAME,
      synchronize: false,
      logging: process.env.PGS_ENVIRONMENT !== "production",
      entities: [AuditLog, Operation],
      subscribers: [],
      options: {
        encrypt: true,
        enableArithAbort: true,
        trustServerCertificate: false,
      },
      extra: {
        validateConnection: false,
        trustServerCertificate: false,
      },
      connectionTimeout: 30000,
      requestTimeout: 30000,
      migrationsRun: false,
      migrationsTransactionMode: "all",
    })
  : new DataSource({
      type: "mssql",
      host: process.env.DATABASE_HOST,
      database: process.env.DATABASE_NAME,
      options: {
        encrypt: true,
      },
      authentication: {
        type: "azure-active-directory-access-token",
        options: {
          token: "", // will be set dynamically below
        },
      },
      entities: [AuditLog, Operation],
      logging: true,
    });

export default AppDataSource;
