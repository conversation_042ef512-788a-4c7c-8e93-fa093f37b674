import dayjs from "dayjs";
import { CandidateStatusEnum } from "@/app/enums/CandidateStatusEnum";
import { getDeliveredFiles } from "@/app/lib/getDeliveredFiles";
import { TestPartsEnum } from "@/app/enums/TestParts";
import { FaTimesCircle } from "react-icons/fa";
import "dayjs/locale/nb";
import { IUserSessionData } from "@/app/interfaces/IUserSessionData";
import { getUserSessionData } from "@/app/lib/getUserSessionData";
import { getStatusInfoFromPgsa } from "@/app/lib/getStatusInfoFromPgsa";
import { getTranslations } from "next-intl/server";
import { IDeliveredFile } from "@/app/interfaces/IUploadFile";

dayjs.locale("nb");

function getTestPart(testPartID: number) {
  switch (testPartID) {
    case TestPartsEnum.Eksamen:
      return "Eksamen";
    case TestPartsEnum.EksamenDel1:
      return "Eksamen del 1";
    case TestPartsEnum.EksamenDel2:
      return "Eksamen del 2";
    case TestPartsEnum.EksamenDel1ogDel2:
      return "Eksamen del 1 og del 2";
    default:
      return "Ikke angitt";
  }
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 B";
  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + " " + sizes[i];
}

const SubmittedFiles = async () => {
  const userSessionData: IUserSessionData = await getUserSessionData();

  const [statusInfo, t] = await Promise.all([
    getStatusInfoFromPgsa(userSessionData.userId),
    getTranslations("Kvittering"),
  ]);

  let deliveredExams: IDeliveredFile[] = [];
  if (statusInfo && statusInfo.Status === CandidateStatusEnum.Levert) {
    try {
      deliveredExams = await getDeliveredFiles(
        userSessionData.candidateNumber,
        userSessionData.candidateGroupCode,
        true
      );
    } catch (error) {
      return (
        <div
          role="alert"
          className="alert alert-error flex items-center gap-2 mt-4"
        >
          <FaTimesCircle />
          <span>{t("ErrorGettingFiles")}</span>
        </div>
      );
    }
  }

  return (
    <>
      <h2 className="text-2xl font-semibold mb-6">{t("DeliveredFiles")}</h2>
      <div className="flex flex-col w-2/3 gap-1">
        {statusInfo &&
        statusInfo.Status === CandidateStatusEnum.LevertManuelt ? (
          <p>{t("MessageDeliveredOnPaper")}</p>
        ) : (
          <>
            {deliveredExams.map((file, index) => (
              <div
                key={index}
                className="flex flex-col sm:flex-row bg-white p-3 gap-4 sm:items-center text-sm"
              >
                <div className="flex-grow break-words font-medium">
                  {file.fileName}
                </div>
                <div className="text-gray-500 whitespace-nowrap">
                  {formatFileSize(file.size)}
                </div>
                <div className="w-40 flex-col gap-6 justify-end justify-items-end sm:text-right">
                  {file.testPartId !== TestPartsEnum.Eksamen && (
                    <div className="whitespace-nowrap font-medium">
                      {getTestPart(file.testPartId)}
                    </div>
                  )}
                  <div className="whitespace-nowrap">
                    kl {dayjs(file.uploadDate).format("HH:mm")}
                  </div>
                </div>
              </div>
            ))}
          </>
        )}
      </div>
    </>
  );
};

export default SubmittedFiles;
