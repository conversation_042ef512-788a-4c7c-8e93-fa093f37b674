"use client";

import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";

interface ErrorPageProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function ErrorPage({ error, reset }: ErrorPageProps) {
  return (
    <div className="flex flex-col gap-6 mb-8">
      <Alert variant="destructive" className="text-error bg-white">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Feil oppstått</AlertTitle>
        <AlertDescription>
          Det oppstod en feil ved lasting av eksamensoppgaven. Prøv å laste siden på nytt.
        </AlertDescription>
      </Alert>
      <button
        onClick={reset}
        className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
      >
        Prøv igjen
      </button>
    </div>
  );
}
