import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateAuditlogTable1749205961105 implements MigrationInterface {
    name = 'CreateAuditlogTable1749205961105'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "AuditLog" ("AuditLogID" int NOT NULL IDENTITY(1,1), "KandidatpaameldingID" varchar(255) NOT NULL, "Timestamp" datetime NOT NULL, "Rolle" varchar(255) NOT NULL, "KandidatNr" varchar(11) NOT NULL, "KandidatFornavn" varchar(255) NOT NULL, "KandidatEtternavn" varchar(255) NOT NULL, "IP" varchar(45) NOT NULL, "Device" varchar(255) NOT NULL, "OS" varchar(255) NOT NULL, "Browser" varchar(255) NOT NULL, "BrowserE<PERSON>" varchar(255) NOT NULL, "Operasjonstype" varchar(255), "OperasjonBeskrivelse" text NOT NULL, "Eksamensdel" varchar(255), "Filnavn" varchar(255), CONSTRAINT "PK_e4113bd09b104be9a506871e440" PRIMARY KEY ("AuditLogID"))`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "AuditLog"`);
    }

}
