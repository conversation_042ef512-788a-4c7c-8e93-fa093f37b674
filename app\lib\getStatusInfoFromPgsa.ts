"use server";

import { getPgsaStatusInfoFromRedis } from "./redisHelper";
import { IStatusInfo } from "../interfaces/IStatusInfo";

export async function getStatusInfoFromPgsa(
  userId: string
): Promise<IStatusInfo | null> {
  let userStatusFromPgsa: IStatusInfo | null = null;

  try {
    userStatusFromPgsa = await getPgsaStatusInfoFromRedis(userId);

    return userStatusFromPgsa;
  } catch (error) {
    throw error;
  } finally {
  }
}
