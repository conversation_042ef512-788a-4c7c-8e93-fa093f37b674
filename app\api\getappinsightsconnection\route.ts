import { getUserSessionData } from "@/app/lib/getUserSessionData";
import { NextResponse } from "next/server";

const connectionString = process.env.CUSTOMCONNSTR_APPINSIGHTS_CONNECTIONSTRING || "";

export const dynamic = "force-dynamic";

export async function GET() {
  const userSessionData = await getUserSessionData();

  if (!userSessionData?.userSessionId) {
    return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
  }

  return NextResponse.json({ connectionString: connectionString });
}
