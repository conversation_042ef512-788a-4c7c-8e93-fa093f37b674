import { logException } from "./appInsightsClient";

// H<PERSON>lpefunksjon for å hente blob-data fra Azure Blob Storage
export async function fetchBlobData(
  blobName: string,
  containerName: string,
  download: boolean
) {
  try {
    const sasResponse = await fetch(
      `${window.location.origin}/api/getblobsastoken?blobname=${blobName}&container=${containerName}&download=${download}`,
      { cache: "no-store", next: { revalidate: 0 } }
    );
    if (!sasResponse.ok) {
      throw new Error(
        `Klarte ikke å hente sastoken for filen. Feilkode:${sasResponse.status}. Feilmelding: ${sasResponse.statusText}`
      );
    }
    const sasData = await sasResponse.json();
    const sasTokenUrl = sasData.sastoken;

    const blobResponse = await fetch(sasTokenUrl, {
      method: "GET",
      cache: "no-store",
      next: { revalidate: 0 },
      headers: {
        Pragma: "no-cache",
        "Cache-Control": "no-cache, no-store, must-revalidate",
        Expires: "0",
      },
    });

    if (!blobResponse.ok) {
      throw new Error(
        `Klarte ikke å hente filen. Feilkode:${blobResponse.status}. Feilmelding: ${blobResponse.statusText}`
      );
    }
    return await blobResponse.blob();
  } catch (error) {
    await logException(error as Error, {
      component: "getBlob",
      action: "fetchBlobData",
      blobName: blobName,
      containerName: containerName,
    });
    throw error;
  }
}

export async function fetchBlobDataIos(
  blobName: string,
  containerName: string,
  download: boolean,
  onProgress?: (progress: number) => void
): Promise<Blob> {
  try {
    const sasResponse = await fetch(
      `${window.location.origin}/api/getblobsastoken?blobname=${blobName}&container=${containerName}&download=${download}`,
      { cache: "no-store" }
    );

    if (!sasResponse.ok) {
      throw new Error(
        `Failed to fetch SAS token. Status: ${sasResponse.status}. Message: ${sasResponse.statusText}`
      );
    }

    const sasData = await sasResponse.json();
    const sasTokenUrl = sasData.sastoken;

    const blobResponse = await fetch(sasTokenUrl, {
      method: "GET",
      cache: "no-store",
      next: { revalidate: 0 },
      headers: {
        Pragma: "no-cache",
        "Cache-Control": "no-cache, no-store, must-revalidate",
        Expires: "0",
      },
    });

    if (!blobResponse.ok) {
      throw new Error(`Failed to fetch blob. Status: ${blobResponse.status}`);
    }

    const reader = blobResponse.body?.getReader();
    const contentLength = +(blobResponse.headers.get("Content-Length") ?? "0");
    let receivedLength = 0;
    const chunks = [];

    while (true) {
      const { done, value } = await reader?.read()!;
      if (done) break;
      chunks.push(value);
      receivedLength += value.length;
      if (onProgress) {
        onProgress((receivedLength / contentLength) * 100);
      }
    }

    return new Blob(chunks);
  } catch (error) {
    await logException(error as Error, {
      component: "getBlob",
      action: "fetchBlobDataIos",
      blobName: blobName,
      containerName: containerName,
    });
    throw error;
  }
}

// Nedlasting av blob
export async function getBlob(
  blobName: string,
  fileName: string,
  containerName: string,
  download: boolean = false
): Promise<boolean> {
  return new Promise<boolean>(async (resolve, reject) => {
    await fetchBlobData(blobName, containerName, download)
      .then((blobData) => {
        const objectUrl = URL.createObjectURL(blobData);
        const a = document.createElement("a");
        a.href = objectUrl;
        a.download = fileName || blobName;

        document.body.appendChild(a);
        a.click();

        document.body.removeChild(a);
        URL.revokeObjectURL(objectUrl);

        resolve(true);
      })
      .catch((error) => {
        console.error("Feil ved nedlasting av blob:", error);

        reject();
      });
  });
}

export function getBlobIos(
  blobName: string,
  fileName: string,
  containerName: string,
  download: boolean = false,
  onProgress?: (progress: number, completed: boolean) => void
): Promise<boolean> {
  return new Promise(async (resolve, reject) => {
    try {
      const blobData = await fetchBlobDataIos(
        blobName,
        containerName,
        download,
        (progress) => {
          if (onProgress) {
            onProgress(progress, progress === 100);
          }
        }
      );

      const objectUrl = URL.createObjectURL(blobData);
      const a = document.createElement("a");
      a.href = objectUrl;
      a.download = fileName || blobName;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(objectUrl);
      resolve(true);
    } catch (error) {
      console.error("Error downloading blob:", error);
      reject(false);
    }
  });
}

// Henting av blob for forhåndsvisning
export async function getBlobForPreview(
  blobName: string,
  fileName: string,
  containerName: string
): Promise<string | undefined> {
  try {
    const blobData = await fetchBlobData(blobName, containerName, true);
    const objectUrl = URL.createObjectURL(blobData);
    return objectUrl;
  } catch (error) {
    console.error("Feil ved nedlasting av blob:", error);
    //throw new Error("Feil ved nedlasting av blob:")
  }
}
