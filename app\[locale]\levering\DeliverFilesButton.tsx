"use client";

import { CandidateStatusEnum } from "@/app/enums/CandidateStatusEnum";
import { deliverExamAction } from "@/app/lib/deliverExamAction";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { useRouter } from "next/navigation";
import React, { useState, useEffect } from "react";
import { useActionState } from "react";
import { FaCircle } from "react-icons/fa";
import { useUploadedFiles } from "./UploadedFilesContext";
import { useFormStatus } from "react-dom";

interface DeliverFilesButtonProps {
  deliverFilesLabel: string;
  duplicateNameError: string;
  filesizeError: string;
  filetypeNotAllowed: string;
  fileUploadError: string;
  downLoadCheckDeliver: string;
  noFilesDeliverLabel: string;
  ConfirmationFileDeliveryLabel: string;
  IConfirmLabel: string;
  FilesAreCorrectLabel: string;
  CannotEditAnswerLabel: string;
  ConfirmDeliveryLabel: string;
  CancelLabel: string;
}

const DeliverFilesButton: React.FC<DeliverFilesButtonProps> = ({
  deliverFilesLabel,
  duplicateNameError,
  filesizeError,
  filetypeNotAllowed,
  fileUploadError,
  downLoadCheckDeliver,
  noFilesDeliverLabel,
  ConfirmationFileDeliveryLabel,
  IConfirmLabel,
  FilesAreCorrectLabel,
  CannotEditAnswerLabel,
  ConfirmDeliveryLabel,
  CancelLabel,
}) => {
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [deliverAttempted, setDeliverAttempted] = useState(false);
  const initialState = {
    errorMessage: "",
    userStatus: CandidateStatusEnum.InnloggetAutentisert,
  };
  const [state, formAction] = useActionState(deliverExamAction, initialState);
  const router = useRouter();
  const { uploadedFiles } = useUploadedFiles();

  const handleDeliverClick = () => {
    setDeliverAttempted(true);
    const hasErrors = uploadedFiles.some(
      (file) => file.isRejected || !file.checked
    );
    if (!hasErrors) {
      setShowConfirmModal(true);
    }
  };

  const getErrorMessage = (fileName: string, errorType: string): string => {
    const errorMessages: { [key: string]: string } = {
      "file-conflicting-name": duplicateNameError,
      "file-too-large": filesizeError,
      "file-invalid-type": filetypeNotAllowed,
    };
    return `'${fileName}' ${errorMessages[errorType] || fileUploadError}`;
  };

  const ErrorMessages = () => {
    const hasErrors = uploadedFiles.some(
      (file) => file.isRejected || !file.checked
    );
    if (!deliverAttempted || !hasErrors) return null;

    return (
      <div className="px-4 py-2 bg-red-50 text-red-700 rounded-sm text-sm border border-red-400">
        <ul className="list-disc list-inside">
          {uploadedFiles
            .filter((file) => file.isRejected)
            .map((file, index) => (
              <li key={index}>
                {getErrorMessage(file.fileName, file.error || "")}
              </li>
            ))}
          {uploadedFiles.some((file) => !file.checked && !file.isRejected) && (
            <li className="mb-2">{downLoadCheckDeliver}</li>
          )}
        </ul>
      </div>
    );
  };

  const SubmitButton = () => {
    const { pending } = useFormStatus();
    return (
      <Button
        type="submit"
        disabled={pending}
        className="btn btn-primary normal-case w-36 text-white"
      >
        {pending ? (
          <Loader2 className="mr-2 h-6 w-6 animate-spin text-gray-800" />
        ) : (
          ConfirmDeliveryLabel
        )}
      </Button>
    );
  };

  return (
    <div className="flex flex-col gap-4">
      <ErrorMessages />
      <div>
        <Button
          variant="default"
          disabled={
            uploadedFiles.length === 0 ||
            uploadedFiles.some((file) => file.uploadFinished === false)
          }
          className="text-white normal-case text-base bg-black p-6"
          onClick={handleDeliverClick}
        >
          {deliverFilesLabel}
        </Button>
      </div>
      {showConfirmModal && (
        <div className="modal modal-open">
          <div className="modal-box rounded bg-white">
            <h3 className="text-3xl">{ConfirmationFileDeliveryLabel}</h3>
            <div className="py-4 break-words flex flex-col gap-4">
              <p className="text-base">{IConfirmLabel}:</p>
              <ul className="ml-2">
                {[FilesAreCorrectLabel, CannotEditAnswerLabel].map(
                  (text, index) => (
                    <li key={index} className="flex gap-2 items-center">
                      <FaCircle role="img" aria-label="liten sirkel" size={8} />
                      {text}
                    </li>
                  )
                )}
              </ul>
            </div>
            <div className="modal-action">
              <form action={formAction}>
                <input
                  type="hidden"
                  name="uploadedFiles"
                  value={JSON.stringify(uploadedFiles)}
                />
                <SubmitButton />
              </form>
              <button
                onClick={() => setShowConfirmModal(false)}
                className="btn normal-case"
              >
                {CancelLabel}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DeliverFilesButton;
