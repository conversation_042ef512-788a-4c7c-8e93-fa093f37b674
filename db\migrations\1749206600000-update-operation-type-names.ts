import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateOperationTypeNames1749206600000 implements MigrationInterface {
    name = 'UpdateOperationTypeNames1749206600000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Task 1: Change EksamensdelEndret and EksamensdelEndretGruppe to Besvarelsesdel
        await queryRunner.query(`
            UPDATE "Operation"
            SET "Operasjonstype" = 'Besvarelsesdel'
            WHERE "OperationID" IN (124, 134);
        `);

        // Task 2: Change Sjekking to Autorisering for specific operations
        await queryRunner.query(`
            UPDATE "Operation"
            SET "Operasjonstype" = 'Autorisering'
            WHERE "OperationID" IN (5, 6, 7, 8);
        `);

        // Task 3: Change Nedlasting to Sjekking for operation 17
        await queryRunner.query(`
            UPDATE "Operation"
            SET "Operasjonstype" = 'Sjekking'
            WHERE "OperationID" = 17;
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Revert Task 1: Change Besvarelsesdel back to original values
        await queryRunner.query(`
            UPDATE "Operation"
            SET "Operasjonstype" = CASE "OperationID"
                WHEN 124 THEN 'EksamensdelEndret'
                WHEN 134 THEN 'EksamensdelEndretGruppe'
            END
            WHERE "OperationID" IN (124, 134);
        `);

        // Revert Task 2: Change Autorisering back to Sjekking
        await queryRunner.query(`
            UPDATE "Operation"
            SET "Operasjonstype" = 'Sjekking'
            WHERE "OperationID" IN (5, 6, 7, 8);
        `);

        // Revert Task 3: Change Sjekking back to Nedlasting
        await queryRunner.query(`
            UPDATE "Operation"
            SET "Operasjonstype" = 'Nedlasting'
            WHERE "OperationID" = 17;
        `);
    }
}
