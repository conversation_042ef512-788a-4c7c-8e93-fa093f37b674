import FileUploadDropzone from "./FileUploadDropzone";
import Link from "next/link";
import { IoArrowBackOutline } from "react-icons/io5";
import { getTranslations } from "next-intl/server";
import { getUserSessionData } from "@/app/lib/getUserSessionData";
import { RouteEnum } from "@/app/enums/RouteEnum";
import CandidateInfo from "@/components/candidateInfo";
import { IUserSessionData } from "@/app/interfaces/IUserSessionData";
import { getSubjectCodeFileSizes } from "@/app/lib/getSubjectCodeFileSizes";
import { getStatusInfoFromPgsa } from "@/app/lib/getStatusInfoFromPgsa";
import { IStatusInfo } from "@/app/interfaces/IStatusInfo";
import { getAllowedMimeTypes } from "@/app/lib/getAllowedMimeTypes";
import { UploadedFilesProvider } from "./UploadedFilesContext";
import UploadedFilesTable from "./UploadedFilesTable";
import DeliverFilesButton from "./DeliverFilesButton";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTrigger,
} from "@/components/ui/dialog";
import { getAppInsightsServer } from "@/app/lib/appInsightsServer";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "PGS - Levering",
  description: "Levering av eksamensoppgave",
};

const telemetryClient = getAppInsightsServer();

const Levering = async () => {
  let userSessionData: IUserSessionData | undefined,
    t,
    pgsaStatusInfo: IStatusInfo | null;

  try {
    userSessionData = await getUserSessionData();
    //  await checkAccessAndNavigate(RouteEnum.Levering, userSessionData);

    [pgsaStatusInfo, t] = await Promise.all([
      getStatusInfoFromPgsa(userSessionData.userId),
      getTranslations("Levering"),
    ]);
  } catch (error) {
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        CandidateNumber: userSessionData?.candidateNumber,
        ExamGroupCode: userSessionData?.candidateGroupCode,
        Message: "Error retrieving data in Levering",
      },
    });

    throw error;
  }

  let fileSizeResponse: ISubjectCodeFileSizes[],
    mimeTypeResponse: IAllowedMimeTypes[];

  try {
    [fileSizeResponse, mimeTypeResponse] = await Promise.all([
      getSubjectCodeFileSizes(),
      getAllowedMimeTypes(),
    ]);
  } catch (error) {
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        CandidateNumber: userSessionData?.candidateNumber,
        ExamGroupCode: userSessionData?.candidateGroupCode,
        Message: "Error retrieving file size data or allowed mimetypes",
      },
    });

    throw error;
  }

  const filesizeLimit =
    fileSizeResponse.find(
      (item) => item.FagKode === (pgsaStatusInfo?.SubjectCode ?? "")
    )?.UploadFileSizeMegaBytes ?? 40;

  return (
    <>
      <div className="flex flex-col gap-6 mb-64 sm:mb-8">
        <Link href={RouteEnum.Eksamensoppgave} className="items-start w-28">
          <div className="flex items-center gap-1 link mb-6 text-lg group">
            <IoArrowBackOutline
              role="img"
              aria-label="Pil venstre"
              className="transition-transform duration-200 ease-in-out group-hover:-translate-x-1"
            />
            {t("Tilbake")}
          </div>
        </Link>

        <div className="flex flex-col gap-6">
          <div className="flex flex-col gap-4">
            <h1 className="text-5xl font-bold">{t("HeadingMessage")}</h1>
            <CandidateInfo
              candidateNumber={pgsaStatusInfo?.CandidateNumber ?? ""}
              subjectCode={pgsaStatusInfo?.SubjectCode ?? ""}
            />
          </div>
          <div>
            <UploadedFilesProvider>
              <div className="flex flex-col gap-6">
                <div className="flex gap-4 md:gap-8">
                  <div>
                    <div className="px-3 py-1 bg-primary rounded-full text-white">
                      1
                    </div>
                  </div>
                  <div className="flex-grow flex flex-col gap-20">
                    <div className="flex flex-col gap-4">
                      <h2 className="flex flex-col sm:flex-row sm:items-center font-semibold text-lg gap-2">
                        {t("UploadLoadYourFiles")}
                        <span className="text-sm font-normal">
                          (Maks {filesizeLimit} MB pr. fil)
                        </span>
                      </h2>
                      <FileUploadDropzone
                        examGroupCode={userSessionData.candidateGroupCode}
                        candidateNumber={userSessionData.candidateNumber}
                        examCodesWithCustomFilesizes={fileSizeResponse}
                        allowedMimeTypes={mimeTypeResponse}
                        uploadInProgress={t("UploadInProgress")}
                        dragFilesHere={t("DragFilesHere")}
                        uploadLoadYourFiles={t("UploadLoadYourFiles")}
                        uploadFileLabel={t("UploadFileLabel")}
                        orLabel={t("Or")}
                        subjectCode={pgsaStatusInfo?.SubjectCode ?? ""}
                        tooManyFilesTitle={t("TooManyFilesTitle")}
                        tooManyFilesMessage={t("TooManyFilesMessage")}
                        testPartId={pgsaStatusInfo?.TestPartId ?? 0}
                      />
                    </div>
                  </div>
                </div>
                <div>
                  <div className="flex gap-4 md:gap-8">
                    <div>
                      <div className="px-3 py-1 bg-primary rounded-full text-white">
                        2
                      </div>
                    </div>
                    <div className="flex flex-col gap-4">
                      <h2
                        className="font-semibold text-lg"
                        id="uploadedFilesTable"
                      >
                        {t("CheckYourFiles")}
                      </h2>
                      <p>
                        {t.rich("CheckFilesMessage", {
                          link: (chunks) => (
                            <Link
                              href="https://kandidat.udir.no"
                              target="_blank"
                              rel="noopener noreferrer"
                              className="underline text-blue-700"
                            >
                              {chunks}
                            </Link>
                          ),
                        })}
                      </p>
                      <div>
                        <Dialog>
                          <DialogTrigger className="link" name="godkjenteFiltyper" aria-label="Gyldige filtyper" aria-controls="filtyper">
                            <span id="filtyper"> Se hvilke filtyper som godkjennes</span>
                          </DialogTrigger>
                          <DialogContent className="sm:max-w-md bg-white p-6" id="godkjenteFiltyper" aria-labelledby="godkjenteFiltyper">
                            <DialogHeader>
                              <DialogDescription>
                                {Array.from(
                                  new Set(
                                    mimeTypeResponse.map((mimeType) =>
                                      mimeType.FileExtension.replace(".", "")
                                    )
                                  )
                                ).join(", ")}
                              </DialogDescription>
                            </DialogHeader>
                          </DialogContent>
                        </Dialog>
                      </div>
                      <UploadedFilesTable
                        deleteLabel={t("Delete")}
                        downLoadLabel={t("DownLoad")}
                        UploadDateLabel={t("UploadDate")}
                        SizeLabel={t("Size")}
                        errorLoadFilesMessage={t("ErrorLoadFiles")}
                        uploadFailedLabel={t("UploadFailed")}
                        notUniqueFilnameLabel={t("NotUniqueFilname")}
                        filesizeTooBigLabel={t("FilesizeTooLarge")}
                        fileNotAllowedLabel={t("FileNotAllowed")}
                        checkedLabel={t("Checked")}
                        notCheckedLabel={t("NotChecked")}
                        confirmDeleteHeading={t("ConfirmDeleteHeading")}
                        confirmDeleteMessage={t("ConfirmDeleteMessage")}
                        confirmDeleteConfirmBtn={t("ConfirmDeleteConfirmBtn")}
                        cancelLabel={t("Cancel")}
                        deleteFailedLabel={t("DeleteFailed")}
                        downloadFailedLabel={t("DownloadFailed")}
                        emptyFileLabel={t("EmptyFile")}
                      />
                    </div>
                  </div>
                </div>
                <div className="flex gap-4 md:gap-8">
                  <div>
                    <div className="px-3 py-1 bg-primary rounded-full text-white">
                      3
                    </div>
                  </div>
                  <div className="flex flex-col gap-4">
                    <h2 className="font-semibold text-lg">
                      {t("DeliverFiles")}
                    </h2>
                    <DeliverFilesButton
                      deliverFilesLabel={t("DeliverFiles")}
                      duplicateNameError={t("DuplicateNameError")}
                      filesizeError={t("FilesizeError")}
                      filetypeNotAllowed={t("FiletypeNotAllowed")}
                      fileUploadError={t("FileUploadError")}
                      downLoadCheckDeliver={t("DownLoadCheckDeliver")}
                      noFilesDeliverLabel={t("NoFilesDeliver")}
                      ConfirmationFileDeliveryLabel={t(
                        "ConfirmationFileDelivery"
                      )}
                      IConfirmLabel={t("IConfirm")}
                      FilesAreCorrectLabel={t("FilesAreCorrect")}
                      CannotEditAnswerLabel={t("CannotEditAnswer")}
                      ConfirmDeliveryLabel={t("ConfirmDelivery")}
                      CancelLabel={t("Cancel")}
                    />
                  </div>
                </div>
              </div>
            </UploadedFilesProvider>
          </div>
        </div>
      </div>
    </>
  );
};

export default Levering;
