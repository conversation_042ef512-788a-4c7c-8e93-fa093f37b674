import { FaCircle } from "react-icons/fa";

interface DeliverExamModalProps {
  ConfirmationFileDeliveryLabel: string;
  IConfirmLabel: string;
  FilesAreCorrectLabel: string;
  CannotEditAnswerLabel: string;
  ConfirmDeliveryLabel:string;
  CancelLabel:string;
}

function DeliverExamModal({
  ConfirmationFileDeliveryLabel,
  IConfirmLabel,
  FilesAreCorrectLabel,
  CannotEditAnswerLabel,
  ConfirmDeliveryLabel,
  CancelLabel
}: DeliverExamModalProps) {
  // if (!isOpen) return null;

  return (
    <div className="modal modal-open">
      <div className="modal-box rounded">
        <h3 className="font-bold text-lg">{ConfirmationFileDeliveryLabel}</h3>
        <div className="py-4 break-words flex flex-col gap-4">
          <span className="font-semibold">{IConfirmLabel}:</span>
          <ul className="ml-2">
            <li className="flex gap-2 items-center flex-row">
              <FaCircle role="img" aria-label="liten sirkel" size={8} />
              {FilesAreCorrectLabel}
            </li>
            <li className="flex gap-2 items-center">
              <FaCircle role="img" aria-label="liten sirkel" size={8}/>
              {CannotEditAnswerLabel}
            </li>
          </ul>
        </div>
        <div className="modal-action">
            <button className="btn bg-neutral hover:bg-primary text-white">
              {ConfirmDeliveryLabel}
            </button>
            <button className="btn btn-outline">
              {CancelLabel}
            </button>
          </div>

      </div>
    </div>
  );
}

export default DeliverExamModal;
