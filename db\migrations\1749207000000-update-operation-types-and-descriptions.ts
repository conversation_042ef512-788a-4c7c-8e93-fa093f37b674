import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateOperationTypesAndDescriptions1749207000000
  implements MigrationInterface
{
  name = "UpdateOperationTypesAndDescriptions1749207000000";

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Update operation 19 description
    await queryRunner.query(`
            UPDATE "Operation"
            SET "BeskrivelseMal" = 'Kandidaten bekreftet levering. Fremdriftsstatus satt til Levert digitalt. Totalt {antallFiler} filer er levert.'
            WHERE "OperationID" = 19;
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Revert operation 19 description
    await queryRunner.query(`
            UPDATE "Operation"
            SET "BeskrivelseMal" = 'Kandidaten leverte digitalt. Totalt {antallFiler} filer er levert.'
            WHERE "OperationID" = 19;
        `);
  }
}
