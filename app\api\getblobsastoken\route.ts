import { CandidateStatusEnum } from "@/app/enums/CandidateStatusEnum";
import {
  generateSasTokenQueryString,
  SasTokenConfig,
  BlobServiceManager,
} from "@/app/lib/blobHelper"; // Updated import name and added BlobServiceManager
import { getStatusInfoFromPgsa } from "@/app/lib/getStatusInfoFromPgsa";
import { getUserSessionData } from "@/app/lib/getUserSessionData";
import { getHashFromRedis } from "@/app/lib/redisHelper";
import { NextResponse } from "next/server";
import { getAppInsightsServer } from "@/app/lib/appInsightsServer";
import { IUserSessionData } from "@/app/interfaces/IUserSessionData";
import { IStatusInfo } from "@/app/interfaces/IStatusInfo";

export const dynamic = "force-dynamic";

// Constants
const COMPONENT_NAME = "getBlobSasTokenApi";
const VALID_PERMISSIONS = ["r", "d"] as const;
const OPPGAVER_CONTAINER = "oppgaver";
const PGS_URL = process.env.PGS_URL;

// Type for valid permissions
type Permission = (typeof VALID_PERMISSIONS)[number];

const OPPGAVER_TOKEN_CONFIG: SasTokenConfig = {
  startOffsetMinutes: 2,
  expiryMinutes: 10, // Token valid for 10 minutes
  cacheDurationMinutes: 8, // Cache for 10 minutes
} as const;

const TOKEN_CONFIG: SasTokenConfig = {
  startOffsetMinutes: 2,
  expiryMinutes: 10, // Token valid for 10 minutes
  cacheDurationMinutes: 8, // Cache for 10 minutes
} as const;

// Interface for API response
interface ApiResponse {
  sastoken?: string;
  message?: string;
}

const telemetryClient = getAppInsightsServer();

function isUserAuthorized(
  isAuthorized: string | null,
  pgsaInfo?: IStatusInfo
): boolean {
  return (
    isAuthorized === "true" &&
    pgsaInfo?.Status !== undefined &&
    [
      CandidateStatusEnum.LastetOpp,
      CandidateStatusEnum.InnloggetAutentisert,
    ].includes(pgsaInfo.Status)
  );
}

function validateRequestParams(
  blobName: string | null,
  container: string | null
): void {
  if (!blobName || !container) {
    throw new Error("Mangler blobnavn eller containernavn i forespørselen");
  }
}

function getTelemetryProperties(
  action: string,
  userSessionData?: IUserSessionData,
  pgsaInfo?: IStatusInfo
) {
  return {
    component: COMPONENT_NAME,
    action,
    userSessionData: userSessionData,
    pgsaInfo: pgsaInfo,
  };
}

export async function GET(request: Request) {
  let userSessionData;

  try {
    userSessionData = await getUserSessionData();

    if (!userSessionData?.userSessionId) {
      telemetryClient?.trackException({
        exception: new Error("Unauthorized - Missing user Guid"),
        properties: getTelemetryProperties("Unauthorized", userSessionData),
      });

      return NextResponse.json(
        { message: "Unauthorized - Missing user session" },
        { status: 401 }
      );
    }

    const userKey = `candidate:${userSessionData.candidateNumber}:${userSessionData.userSessionId}`;

    const [isAuthorized, pgsaInfo] = await Promise.all([
      getHashFromRedis(userKey, "isAuthorized"),
      getStatusInfoFromPgsa(userSessionData.userId),
    ]);

    if (!isUserAuthorized(isAuthorized, pgsaInfo ?? undefined)) {
      telemetryClient?.trackException({
        exception: new Error(
          "NotAuthorized - User not authorized to get blob-token"
        ),
        properties: {
          ...getTelemetryProperties(
            "NotAuthorized",
            userSessionData,
            pgsaInfo ?? undefined
          ),
        },
      });

      return NextResponse.json(
        { message: "User not authorized to get blob-token" },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const blobName = searchParams.get("blobname");
    const container = searchParams.get("container");
    const download = searchParams.get("download");

    try {
      if (!container) {
        throw new Error("Mangler containernavn i forespørselen");
      }
    } catch (error) {
      return NextResponse.json(
        { message: (error as Error).message },
        { status: 400 }
      );
    }

    // Generate SAS token using the unified blobHelper
    const permission = (download === "true" ? "r" : "d") as Permission;

    // Use container-level SAS if no specific blob is requested
    const sasToken = await generateSasTokenQueryString(
      container,
      "", // Empty string for container-level access
      permission,
      container === OPPGAVER_CONTAINER ? OPPGAVER_TOKEN_CONFIG : TOKEN_CONFIG,
      true
    );

    const response: ApiResponse = {
      sastoken: `${PGS_URL}/${container}/${blobName}?${sasToken}`,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error in getBlobSasTokenApi:", error);

    telemetryClient?.trackException({
      exception: error as Error,
      properties: getTelemetryProperties("Error", userSessionData),
    });

    return NextResponse.json(
      { message: "Internal Server Error" },
      { status: 500 }
    );
  }
}
