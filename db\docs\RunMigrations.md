# TypeORM Migrasjonsprosess

## Steg 1: Lage Ny eller Endre Modellen
Modifiser modellen din i TypeScript for å reflektere ønskede endringer.

**Eksempel:**
```typescript
import "reflect-metadata";
import { Entity, PrimaryGeneratedColumn, Column } from "typeorm";

@Entity("AuditLog")
export class AuditLog {
  @PrimaryGeneratedColumn({ name: "AuditLogID" })
  AuditLogID!: number;

  // Endre KandidatpaameldingID fra int til string
  @Column("varchar", { name: "KandidatpaameldingID", length: 255 })
  KandidatpaameldingID!: string;  
}
```

## Steg 2: Generer Migrasjonen
```bash
npx typeorm-ts-node-esm migration:generate db/migrations/rename-multiple-columns -d db/migrations-data-source.ts
```

## Steg 3: Sjekk Migrasjonsfilen
```typescript
import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateKandidatpaameldingIDToString1732200000000
  implements MigrationInterface
{
  name = "1732200000000-rename-columns";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "AuditLog"
      DROP COLUMN "KandidatpaameldingID";
    `);

    await queryRunner.query(`
      ALTER TABLE "AuditLog"
      ADD "KandidatpaameldingID" varchar(255) NOT NULL;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "AuditLog"
      DROP COLUMN "KandidatpaameldingID";
    `);

    await queryRunner.query(`
      ALTER TABLE "AuditLog"
      ADD "KandidatpaameldingID" int NOT NULL;
    `);
  }
}
```

## Steg 4: Kjør Migrasjonen
```bash
npx typeorm-ts-node-esm migration:run -d db/migrations/migrations-data-source.ts
```

## Steg 5: Rull Tilbake Migrasjonen (om nødvendig)
```bash
npx typeorm-ts-node-esm migration:revert -d db/migrations/migrations-data-source.ts
```