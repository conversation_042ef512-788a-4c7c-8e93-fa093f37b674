"use server";

import { getValueFromRedis, setValueInRedis } from "./redisHelper";

interface TokenResponse {
  access_token: string;
  expires_in: number;
  token_type: string;
}

const clientId: string = process.env.UIDP_PGSA_BESVARELSE_CLIENT_ID || "";
const clientSecret: string =
  process.env.UIDP_PGSA_BESVARELSE_CLIENT_SECRET || "";
const scope: string = process.env.UIDP_PGSA_BESVARELSE_API_SCOPE || "";
const tokenEndpoint: string = `${process.env.NEXTAUTH_UIDP_URL}/connect/token`;
const accesstokenKey: string = "PGSK:PGSA:AccessToken";
let tokenExpiresInSeconds: number = 2400; // 40 minutter

export async function getPgsaAccessToken(): Promise<string> {
  let accessToken = await getValueFromRedis(accesstokenKey);

  if (accessToken) return JSON.parse(accessToken);

  const params = new URLSearchParams();
  params.append("client_id", clientId);
  params.append("client_secret", clientSecret);
  params.append("grant_type", "client_credentials");
  params.append("scope", scope);

  try {
    const response = await fetch(tokenEndpoint, {
      method: "POST",
      body: params,
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      cache: "no-store",
    });

    if (!response.ok) {
      throw new Error(
        `Feil ved henting av token mot PGSA. Statuskode: ${response.status}. Feilmelding: ${response.statusText}`
      );
    }

    const data: TokenResponse = await response.json();
    accessToken = data.access_token;

    await setValueInRedis(
      accesstokenKey,
      JSON.stringify(accessToken),
      tokenExpiresInSeconds
    );

    return accessToken;
  } catch (error) {
    console.error(
      "Klarte ikke å hente token for å autentisere api-kall:",
      error
    );
    throw new Error("Klarte ikke å hente token for å autentisere api-kall");
  }
}
