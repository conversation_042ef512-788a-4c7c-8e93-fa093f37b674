import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateEksamensdelDescriptions1749208000000
  implements MigrationInterface
{
  name = "UpdateEksamensdelDescriptions1749208000000";

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Update operation 124 description
    // Change "Filen fikk endret eksamensdel til del {partNumber}" to "Filen fikk endret eksamensdel til {partNumber}"
    await queryRunner.query(`
            UPDATE "Operation"
            SET "BeskrivelseMal" = 'Filen fikk endret eksamensdel til {partNumber}'
            WHERE "OperationID" = 124;
        `);

    // Update operation 134 description
    // Change "Filen fikk endret eksamensdel til del {partNumber} via gruppeopplasteren" to "Filen fikk endret eksamensdel til {partNumber} via gruppeopplasteren"
    await queryRunner.query(`
            UPDATE "Operation"
            SET "BeskrivelseMal" = 'Filen fikk endret eksamensdel til {partNumber} via gruppeopplasteren'
            WHERE "OperationID" = 134;
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Revert operation 124 description
    await queryRunner.query(`
            UPDATE "Operation"
            SET "BeskrivelseMal" = 'Filen fikk endret eksamensdel til del {partNumber}'
            WHERE "OperationID" = 124;
        `);

    // Revert operation 134 description
    await queryRunner.query(`
            UPDATE "Operation"
            SET "BeskrivelseMal" = 'Filen fikk endret eksamensdel til del {partNumber} via gruppeopplasteren'
            WHERE "OperationID" = 134;
        `);
  }
}
