"use client";

import { BiArrowToBottom } from "react-icons/bi";
import html2canvas from "html2canvas";
import { jsPDF } from "jspdf";
import { logActivity } from "@/app/lib/logActivity";
import { OperationEnum } from "@/app/enums/OperationEnum";

interface Props {
  downloadRecieptLabel: string;
}

async function downloadReceipt(htmlElement: string) {
  const logoCloneElement = document.createElement("div");
  let inputElement: HTMLElement | null = document.getElementById(htmlElement);
  let originalWidth: string;
  if (inputElement !== null) {
    originalWidth = window.getComputedStyle(inputElement).width;
    inputElement.style.width = "1000px";
  }
  try {
    if (logoCloneElement && inputElement) {
      inputElement.insertBefore(logoCloneElement, inputElement.firstChild);

      html2canvas(inputElement, { scale: 3 }).then((canvas: any) => {
        const imgWidth = 208;
        const pageHeight = 295;
        const aspectRatio = canvas.width / canvas.height;
        const imgHeight = imgWidth / aspectRatio;
        const doc = new jsPDF("p", "mm");
        let heightLeft = imgHeight;
        let position = 0;

        heightLeft -= pageHeight;
        doc.addImage(
          canvas,
          "PNG",
          10, // venstre margin
          position + 10, // topp margin
          imgWidth - 20, // bredde justert for venstre- og høyre margin
          imgHeight - 20, // høyde justert for topp- og bunnmargin
          "",
          "FAST"
        );
        while (heightLeft >= 0) {
          position = heightLeft - imgHeight;
          doc.addPage();
          doc.addImage(
            canvas,
            "PNG",
            10, // venstre margin
            position + 10, // topp margin
            imgWidth - 20, // bredde justert for venstre- og høyre margin
            imgHeight - 20, // høyde justert for topp- og bunnmargin
            "",
            "FAST"
          );
          heightLeft -= pageHeight;
        }
        doc.save("Kvittering.pdf");
        if (inputElement !== null) inputElement.style.width = originalWidth;
      });
    } else {
      console.error("Element with ID 'udir-logo' not found");
    }

    await logActivity("", "", OperationEnum.KvitteringNedlastet);
    
  } catch (error) {
    console.log("Error in downloadReceipt:", error);
  } finally {
    if (logoCloneElement && logoCloneElement.parentNode) {
      logoCloneElement.parentNode.removeChild(logoCloneElement);
    }
  }
}

const DownloadButton = ({ downloadRecieptLabel }: Props) => {
  return (
    <button
      className="btn btn-ghost mb-10"
      data-html2canvas-ignore="true"
      onClick={async () => await downloadReceipt("ExamReceipt")}
    >
      <BiArrowToBottom size={22} role="img" aria-label="Nedlastingsikon" />
      <span>{downloadRecieptLabel}</span>
    </button>
  );
};

export default DownloadButton;
