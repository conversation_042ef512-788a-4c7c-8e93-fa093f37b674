import { Metadata } from "next";
import React from "react";
import { getStatusInfoFromPgsa } from "@/app/lib/getStatusInfoFromPgsa";
import { getTranslations } from "next-intl/server";
import dayjs, { Dayjs } from "dayjs";
import "dayjs/locale/nb";
import utc from "dayjs/plugin/utc";
import Image from "next/image";
import { getUserSessionData } from "@/app/lib/getUserSessionData";
import Countdown from "./countdown";
import { StatusInfoEmptyError } from "@/app/lib/exceptionTypes";
import { AuditLogService } from "@/db/services/auditLogService";
import { getUserLoggingInfo } from "@/app/lib/getUserLoggingInfo";
import { OperationEnum } from "@/app/enums/OperationEnum";
import { sendAuditLogSafe } from "@/app/lib/serviceBusClient";
import { headers } from "next/headers";
import PersonSittingImg from "@/app/assets/PersonSitting.png";

dayjs.extend(utc);
dayjs.locale("nb");

export const metadata: Metadata = {
  title: "PGS - Venteside",
  description: "Vent til eksamen starter",
};

interface ExamScheduleProps {
  examParts: string[];
  examTimeLabel: string;
  examLabel: string;
  testPartId: number;
}

const ExamSchedule: React.FC<ExamScheduleProps> = ({
  examParts,
  examTimeLabel,
  examLabel,
  testPartId,
}) => {
  return (
    <div className="space-y-2">
      <h2 className="font-semibold flex items-center gap-2">
        {examTimeLabel}:
      </h2>
      <div className="flex flex-col items-start gap-2">
        {examParts.map((part, index) => (
          <div key={index} className="w-full">
            <div className="bg-gray-100 py-2 px-4 flex flex-col rounded">
              <span className="text-sm text-gray-700 font-semibold">
                {testPartId === 1 ? examLabel : part.split(": ")[0]}
              </span>
              <span className="text-sm text-gray-600">
                {part.split(": ")[1]}
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

const logExamStatusActivity = async (isPartOne: boolean) => {
  const data = await getUserLoggingInfo(
    isPartOne
      ? OperationEnum.StatusVenterPaaStart
      : OperationEnum.StatusVenterPaaStartDel2
  );

  if (!data) {
    console.error("Failed to get logging info");
    return false;
  }

  // Logg til Service Bus synchronously with improved error handling
  const userAgent = (await headers()).get("user-agent") || "Unknown User Agent";
  const success = await sendAuditLogSafe(data, userAgent, {
    blocking: true,
    fallback: true,
    context: "examStatus"
  });

  if (!success) {
    console.warn("Exam status audit log failed but stored for retry");
  }

  return true;
};

const Klar = async ({}) => {
  const userSessionData = await getUserSessionData();
  const [statusInfo, t] = await Promise.all([
    getStatusInfoFromPgsa(userSessionData.userId),
    getTranslations("Klar"),
  ]);

  if (!statusInfo) throw new StatusInfoEmptyError();

  let startDateUtc: Dayjs = dayjs(statusInfo.TestStartTime);
  let endTimeUtc: Dayjs = dayjs(statusInfo.TestEndTime);

  // Lokal tid for visning
  let startDateLocal: Dayjs = startDateUtc.local();
  let endTimeLocal: Dayjs = endTimeUtc.local();

  let formattedStartTime: string = startDateLocal.format("HH:mm");
  let formattedEndTime: string = endTimeLocal.format("HH:mm");
  let examParts: string[] = [];

  if (statusInfo.TestPartId === 1) {
    examParts.push(`Del 1: ${formattedStartTime} - ${formattedEndTime}`);
    await logExamStatusActivity(true);
  } else {
    let part1Start = startDateLocal.set("hour", 9).set("minute", 0);
    examParts.push(
      `Del 1: ${part1Start.format("HH:mm")} - ${formattedStartTime}`
    );
    examParts.push(`Del 2: ${formattedStartTime} - ${formattedEndTime}`);
    await logExamStatusActivity(false);
  }

  const serverTime = dayjs().toISOString();

  return (
    <div className="flex flex-col gap-6 mb-8">
      <h1 className="text-5xl font-bold ">
        {t("WelcomeMessage", { displayName: userSessionData?.name })}
      </h1>

      <div className="flex flex-col lg:flex-row gap-8">
        <div className="">
          <div className="mt-8">
            <div className="card rounded-none w-full bg-white ">
              <div className="card-body flex flex-col gap-4">
                <div className="font-bold text-xl">
                  {examParts.length > 1 ? (
                    <span>{t("ReadyExamTwoPartsNotStarted")} </span>
                  ) : (
                    <span> {t("ReadyExamNotStarted")}</span>
                  )}
                </div>
                <div>
                  <h2 className="font-semibold">{t("Exam")}:</h2>
                  <div className="text-base">
                    {statusInfo.SubjectName}
                    <span className="hidden sm:inline mx-2">-</span>
                    <span>{statusInfo.SubjectCode}</span>
                  </div>
                </div>
                <div>
                  <ExamSchedule
                    examParts={examParts}
                    examTimeLabel={t("ExamTime")}
                    examLabel={t("Exam")}
                    testPartId={statusInfo.TestPartId}
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="mt-8">
            <div className="card rounded-none w-full bg-white">
              <div className="card-body">
                <Countdown
                  examStartsLabel={t("ExamStartsIn")}
                  twoPartsLabel={t("ExamStartsInTwoParts")}
                  teststartTime={statusInfo.TestStartTime}
                  examParts={examParts}
                  serverTime={serverTime}
                  sendForwardLabel={t("SendForward")}
                  daysLabel={t("days")}
                  dayLabel={t("day")}
                  hoursLabel={t("Hours")}
                  hourLabel={t("Hour")}
                  minutesLabel={t("Minutes")}
                  minuteLabel={t("Minute")}
                  feilLabel={t("feil")}
                  oneMinuteLabel={t("UnderOneMinute")}
                  secondsLabel={t("Seconds")}
                  secondLabel={t("Second")}
                />
              </div>
            </div>
          </div>
        </div>
        <div className="flex justify-center">
          <Image
            src={PersonSittingImg}
            width={500}
            height={500}
            alt={t("PictureSittingPerson")}
            priority={false}
          />
        </div>
      </div>
    </div>
  );
};

export default Klar;
