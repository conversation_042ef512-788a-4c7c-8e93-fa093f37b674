"use client";

import { useEffect, useState } from "react";
import useS<PERSON> from "swr";
import { usePathname } from "next/navigation";
import { RouteEnum } from "./enums/RouteEnum";

interface Props {
  onlineServerUrl: string;
  sessionId: string;
  userId: string;
  candidateNumber: string;
  candidateGroupCode: string;
}

interface IOnlineServerRequest {
  SessionId: string;
  CrId: string;
  CandidateNumber: string;
  ExamGroupCode: string;
  StatusCode: string;
  OS: string;
  Browser: string;
  Device: string;
}

function getSystemInfo(): { os: string; browser: string; device: string } {
  let os = "Ukjent operativsystem";
  let osVersion = "";
  let browser = "Ukjent nettleser";
  let device = "Datamaskin";

  if (typeof window !== "undefined") {
    const userAgent = window.navigator.userAgent;
    const userAgentLower = userAgent.toLowerCase();

    // Detekter operativsystem med versjon
    const osMatches = userAgent.match(
      /(Windows NT|Windows|Android|iPhone|iPad|Mac OS X|Linux)(?:\s|\[|\/)([\d._]+)|(Mac OS|iOS)/
    );

    if (osMatches) {
      const osNameMap: Record<string, string> = {
        "Windows NT": "Windows",
        Windows: "Windows",
        "Mac OS X": "macOS",
        "Mac OS": "macOS",
        Android: "Android",
        iPhone: "iOS",
        iPad: "iOS",
        Linux: "Linux",
        iOS: "iOS",
      };

      const rawName = osMatches[1] || osMatches[3];
      os = osNameMap[rawName] || rawName;

      if (osMatches[2]) {
        osVersion = osMatches[2].replace(/_/g, ".");
        os += ` ${osVersion}`;
      }
    }

    // Detekter nettleser med versjon
    const browserMatch =
      userAgent.match(
        /(Chrome|CriOS|Firefox|FxiOS|Safari|Edg|Edge|Opera|OPR|Vivaldi)\/([\d.]+)/
      ) || userAgent.match(/(MSIE|Trident)(?:\/| )([\d.]+)/);

    if (browserMatch) {
      const browserNameMap: Record<string, string> = {
        Chrome: "Chrome",
        CriOS: "Chrome", // Chrome på iOS
        Firefox: "Firefox",
        FxiOS: "Firefox", // Firefox på iOS
        Safari: "Safari",
        Edg: "Edge",
        Edge: "Edge",
        Opera: "Opera",
        OPR: "Opera",
        Vivaldi: "Vivaldi",
        MSIE: "Internet Explorer",
        Trident: "Internet Explorer",
      };

      const rawName = browserMatch[1];
      const version = browserMatch[2];
      browser = `${browserNameMap[rawName] || rawName} ${version}`;
    }

    // Presis enhetsdeteksjon
    const isTablet =
      /(iPad|Android(?!.*Mobile)|Tablet|Silk|Kindle|PlayBook)/i.test(userAgent);
    const isMobile =
      /(iPhone|iPod|Android.*Mobile|BlackBerry|IEMobile|Opera Mini)/i.test(
        userAgent
      );

    device = isTablet ? "Nettbrett" : isMobile ? "Mobil" : "Datamaskin";
  }

  return { os, browser, device };
}

async function sendHeartbeat(
  url: string,
  data: IOnlineServerRequest
): Promise<void> {
  const response = await fetch(url, {
    method: "POST",
    body: JSON.stringify(data),
    headers: {
      "Content-Type": "application/json",
    },
  });
  if (!response.ok) throw new Error("Kunne ikke sende heartbeat");
}

export const OnlineServer = ({
  onlineServerUrl,
  sessionId,
  userId,
  candidateNumber,
  candidateGroupCode,
}: Props) => {
  const pathname = usePathname();
  const [systemInfo, setSystemInfo] = useState<{
    os: string;
    browser: string;
    device: string;
  } | null>(null);
  const [isClient, setIsClient] = useState(false);

  // Initialize system info on client-side only
  useEffect(() => {
    setIsClient(true);
    setSystemInfo(getSystemInfo());
  }, []);

  // Sjekk om gjeldende URL er /levering eller /eksamensoppgave
  const isValidPath =
    pathname === RouteEnum.Levering || pathname === RouteEnum.Eksamensoppgave;

  // Only run SWR when client is hydrated and we have system info
  useSWR(
    isClient && isValidPath && systemInfo ? "heartbeat" : null,
    async () => {
      const request: IOnlineServerRequest = {
        SessionId: sessionId,
        CrId: userId,
        CandidateNumber: candidateNumber,
        ExamGroupCode: candidateGroupCode,
        StatusCode: "active",
        OS: systemInfo?.os || "Ukjent operativsystem",
        Browser: systemInfo?.browser || "Ukjent nettleser",
        Device: systemInfo?.device || "Datamaskin",
      };

      return sendHeartbeat(onlineServerUrl, request);
    },
    {
      refreshInterval: 90000, // Oppdatere hvert 1.5 minutt
      revalidateOnFocus: false,
      onError: (error) => {
        console.error("Kunne ikke sende heartbeat:", error);
      },
    }
  );

  // Engangskall på hjemsiden - only run when client is hydrated
  useEffect(() => {
    if (isClient && pathname === RouteEnum.Hjem && systemInfo) {
      const request: IOnlineServerRequest = {
        SessionId: sessionId,
        CrId: userId,
        CandidateNumber: candidateNumber,
        ExamGroupCode: candidateGroupCode,
        StatusCode: "active",
        OS: systemInfo.os,
        Browser: systemInfo.browser,
        Device: systemInfo.device,
      };

      sendHeartbeat(onlineServerUrl, request).catch((error) => {
        console.error("Kunne ikke sende engangskall på hjemsiden:", error);
      });
    }
  }, [
    isClient,
    pathname,
    systemInfo,
    onlineServerUrl,
    sessionId,
    userId,
    candidateNumber,
    candidateGroupCode,
  ]);

  return null;
};
