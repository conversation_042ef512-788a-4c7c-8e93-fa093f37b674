# Audit Log Database Normalization

Dette dokumentet beskriver den nye normaliserte strukturen for audit logging systemet.

## Oversikt

Den opprinnelige AuditLog tabellen inneholdt redundante data hvor `OPERASJONSTYPE` og `OPERASJON_BESKRIVELSE` ble gjentatt på hver rad. Denne strukturen har blitt normalisert ved å introdusere en separat `Operation` tabell.

## Database Struktur

### Operation Tabell (Ny)
```sql
CREATE TABLE "Operation" (
    "OperationID" int PRIMARY KEY,
    "Operasjonstype" varchar(255) NOT NULL,
    "BeskrivelseMal" text NOT NULL
);
```

**Inneholder:**
- **OperationID**: Unik identifikator (matcher OperationEnum verdier)
- **Operasjonstype**: Type operasjon (Innlogging, Autorisering, etc.)
- **BeskrivelseMal**: Template for beskrivelse med {placeholder} verdier

### AuditLog Tabell (Modifisert)
```sql
-- Fjernet kolonner:
-- "Operasjonstype" varchar(255)

-- Nye kolonner:
ALTER TABLE "AuditLog" ADD "OperationID" int NOT NULL;
ALTER TABLE "AuditLog" ADD "Parameters" nvarchar(MAX);
ALTER TABLE "AuditLog" ADD CONSTRAINT "FK_AuditLog_Operation" 
    FOREIGN KEY ("OperationID") REFERENCES "Operation"("OperationID");
```

**Nye felter:**
- **OperationID**: Foreign key til Operation tabellen
- **Parameters**: JSON string med dynamiske verdier
- **OperasjonBeskrivelse**: Fortsatt finnes, men inneholder nå ferdig formatert tekst

## Kode Endringer

### 1. OperationEnum
```typescript
export enum OperationEnum {
  InnloggingNySesjon = 1,
  TilgangBedt = 2,
  AutorisertMedDagspassord = 3,
  // ... osv
}
```

### 2. IAuditLogData Interface
```typescript
export interface IAuditLogData {
  // ... eksisterende felter
  operationId: OperationEnum;           // Ny: Operation ID
  parameters?: Record<string, any>;     // Ny: JSON parametere
  beskrivelse: string;                  // Endret: Ferdig formatert tekst
  // operasjonstype?: OperationTypeEnum; // Fjernet
}
```

### 3. AuditLogService Nye Metoder
```typescript
// Bygg audit data med template
public async buildAuditLogData(
  baseData: Omit<IAuditLogData, 'operationId' | 'parameters' | 'beskrivelse'>,
  operationId: number,
  parameters?: Record<string, any>
): Promise<IAuditLogData | null>

// Hent operation template
public async getOperationTemplate(operationId: number): Promise<string | null>
```

## Brukseksempler

### Enkel Operasjon (Uten Parametere)
```typescript
const auditService = AuditLogService.getInstance();

const baseData = {
  kandidatpaameldingId: "12345",
  rolle: "Kandidat",
  kandidatNr: "12345678901",
  // ... andre felter
};

const auditData = await auditService.buildAuditLogData(
  baseData,
  OperationEnum.AutorisertMedDagspassord
);

if (auditData) {
  await auditService.logActivity(auditData);
}
```

### Operasjon Med Dynamiske Verdier
```typescript
const auditData = await auditService.buildAuditLogData(
  baseData,
  OperationEnum.LevertDigitalt,
  { antallFiler: 5 }
);

// Resultat: "Kandidaten leverte digitalt. Totalt 5 filer er levert."
```

### Direkte Logging
```typescript
const auditData: IAuditLogData = {
  // ... base data
  operationId: OperationEnum.LastetNedOppgave,
  parameters: undefined,
  beskrivelse: "Kandidaten lastet ned oppgavefilen.",
};

await auditService.logActivity(auditData);
```

## Template System

Operation tabellen bruker et template system hvor placeholders erstattes med faktiske verdier:

**Template:** `"Kandidaten leverte digitalt. Totalt {antallFiler} filer er levert."`  
**Parameters:** `{ antallFiler: 5 }`  
**Resultat:** `"Kandidaten leverte digitalt. Totalt 5 filer er levert."`

### Tilgjengelige Templates
| OperationID | Template |
|-------------|----------|
| 1 | `Kandidaten logget inn - ny sesjon startet. Kandidaten kom til {side}` |
| 19 | `Kandidaten leverte digitalt. Totalt {antallFiler} filer er levert.` |
| 21 | `Kandidaten leverte digitalt del 2. Totalt {antallFiler} filer er levert.` |

## Migrasjon

Kjør migrasjonen for å oppdatere databasen:

```bash
npm run migration:run
```

Migrasjonen vil:
1. Opprette Operation tabellen
2. Populere den med eksisterende operasjonsdata
3. Legge til nye kolonner i AuditLog
4. Migrere eksisterende data
5. Fjerne gamle kolonner

## Fordeler

1. **Eliminert Redundans**: Operasjonsbeskrivelser lagres kun én gang
2. **Type Safety**: OperationEnum forhindrer ugyldige operation IDs
3. **Fleksibilitet**: JSON parametere tillater dynamisk innhold
4. **Ytelse**: Direkte ID referanse i stedet for string matching
5. **Vedlikehold**: Enkelt å oppdatere operation templates

## Bakoverkompatibilitet

Eksisterende audit log data vil bli migrert automatisk. Den nye strukturen støtter alle eksisterende operasjoner og beskrivelser.
