# Database konfigurasjon for lokal utvikling
# Kopier denne filen til .env og oppdater verdiene

DATABASE_HOST=localhost
DATABASE_USERNAME=sa
DATABASE_PASSWORD=YourPassword123!
DATABASE_NAME=PGS_DB
PGS_ENVIRONMENT=localhost

# Eksempel for Azure SQL Database
# DATABASE_HOST=your-server.database.windows.net
# DATABASE_USERNAME=your-username
# DATABASE_PASSWORD=your-password
# DATABASE_NAME=your-database
# PGS_ENVIRONMENT=localhost
