#!/usr/bin/env pwsh
# Script for å kjøre TypeORM migrations med miljø-spesifikke .env filer

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("show", "run", "revert", "generate")]
    [string]$Action,
    
    [Parameter(Mandatory=$false)]
    [ValidateSet("local", "dev", "test", "qa", "production")]
    [string]$Environment = "local",
    
    [Parameter(Mandatory=$false)]
    [string]$Name = ""
)

$envFile = ".env.$Environment"

# Sjekk om .env fil eksisterer
if (!(Test-Path $envFile)) {
    Write-Host "❌ .env fil ikke funnet: $envFile" -ForegroundColor Red
    Write-Host "Tilgjengelige miljøer: local, dev, test, qa, production" -ForegroundColor Yellow
    exit 1
}

Write-Host "🌍 Environment: $Environment" -ForegroundColor Green
Write-Host "📁 Env file: $envFile" -ForegroundColor Green

# Last inn miljøvariabler fra .env fil
Get-Content $envFile | ForEach-Object {
    if ($_ -match '^([^=]+)\s*=\s*(.*)$') {
        $key = $matches[1].Trim()
        $value = $matches[2].Trim()
        # Fjern anførselstegn hvis de finnes
        if ($value -match '^[''"](.*)[''""]$') {
            $value = $matches[1]
        }
        [System.Environment]::SetEnvironmentVariable($key, $value)
    }
}

$dataSource = "db/migrations/environments/migrations-data-source-$Environment.ts"
Write-Host "📄 Data source: $dataSource" -ForegroundColor Green
Write-Host "----------------------------------------" -ForegroundColor Cyan

switch ($Action) {
    "show" {
        Write-Host "📋 Viser migration status for $Environment..." -ForegroundColor Blue
        npx typeorm-ts-node-esm migration:show -d $dataSource
    }
    "run" {
        Write-Host "🚀 Kjører migrations for $Environment..." -ForegroundColor Blue
        Write-Host "⚠️  Dette vil endre databasen. Fortsett? (y/N)" -ForegroundColor Yellow
        $confirm = Read-Host
        if ($confirm -eq "y" -or $confirm -eq "Y") {
            npx typeorm-ts-node-esm migration:run -d $dataSource
            Write-Host "✅ Migrations kjørt. Sjekker status..." -ForegroundColor Green
            npx typeorm-ts-node-esm migration:show -d $dataSource
        } else {
            Write-Host "❌ Avbrutt av bruker." -ForegroundColor Red
        }
    }
    "revert" {
        Write-Host "⏪ Reverter siste migration for $Environment..." -ForegroundColor Blue
        Write-Host "⚠️  Dette vil endre databasen. Fortsett? (y/N)" -ForegroundColor Yellow
        $confirm = Read-Host
        if ($confirm -eq "y" -or $confirm -eq "Y") {
            npx typeorm-ts-node-esm migration:revert -d $dataSource
            Write-Host "✅ Migration revertert. Sjekker status..." -ForegroundColor Green
            npx typeorm-ts-node-esm migration:show -d $dataSource
        } else {
            Write-Host "❌ Avbrutt av bruker." -ForegroundColor Red
        }
    }
    "generate" {
        if ([string]::IsNullOrEmpty($Name)) {
            Write-Host "❌ Du må angi et navn for migration med -Name parameteren" -ForegroundColor Red
            Write-Host "Eksempel: .\run-migrations.ps1 -Action generate -Environment local -Name CreateUserTable" -ForegroundColor Yellow
            exit 1
        }
        Write-Host "📝 Genererer migration: $Name" -ForegroundColor Blue
        $timestamp = (Get-Date).ToString('yyyyMMddHHmmss')
        npx typeorm-ts-node-esm migration:generate "db/migrations/$timestamp-$Name" -d $dataSource
    }
}

Write-Host "----------------------------------------" -ForegroundColor Cyan
Write-Host "🎉 Ferdig!" -ForegroundColor Green
