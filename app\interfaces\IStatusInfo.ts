import { CandidateStatusEnum } from "../enums/CandidateStatusEnum";

export interface IStatusInfo {
  ExamGroupCode: string;
  CandidateNumber: string;
  SocialSecurityNumber: string;
  Status: CandidateStatusEnum;
  UserId: string;
  CandidateSurname: string;
  CandidateFirstName: string;
  TestPeriod: string;
  SchoolId: string;
  SchoolName: string;
  SubjectCode: string;
  Variant: string;
  SubjectName: string;
  TestPartId: number;
  AllowElectronicLogin: boolean;
  TestStartTime: string;
  TestEndTime: string;
  TestStartDate: string;
  TestEndDate: string;
  TestType: string;
  Exercise: null;
  OneTimeAuthorized: boolean;
}
