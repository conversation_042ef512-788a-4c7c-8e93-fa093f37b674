import { CandidateStatusEnum } from "@/app/enums/CandidateStatusEnum";
import { IStatusInfo } from "@/app/interfaces/IStatusInfo";
import { StatusInfoEmptyError } from "@/app/lib/exceptionTypes";
import { getClientIp } from "@/app/lib/getClientIp";
import { getStatusInfoFromPgsa } from "@/app/lib/getStatusInfoFromPgsa";
import { getUserSessionData } from "@/app/lib/getUserSessionData";
import { getHashFromRedis } from "@/app/lib/redisHelper";
import { updateCandidateStatus } from "@/app/lib/updateCandidateStatus";
import { NextResponse } from "next/server";

export async function GET(request: Request) {
  const userSessionData = await getUserSessionData();

  if (!userSessionData?.userSessionId)
    return NextResponse.json({ message: "Unauthorized" }, { status: 401 });

  const userKey = `candidate:${userSessionData.candidateNumber}:${userSessionData.userSessionId}`;
  const isAuthorized = await getHashFromRedis(userKey, "isAuthorized");

  if (isAuthorized !== "true")
    return NextResponse.json(
      { message: "User not authorized" },
      { status: 403 }
    );

  const statusInfo: IStatusInfo | null = await getStatusInfoFromPgsa(
    userSessionData.userId
  );

  if (!statusInfo) throw new StatusInfoEmptyError();

  if (statusInfo.Status === CandidateStatusEnum.LastetOpp) {
    await updateCandidateStatus(
      statusInfo.Status,
      CandidateStatusEnum.InnloggetAutentisert,
      statusInfo.TestPartId,
      statusInfo.CandidateNumber,
      statusInfo.ExamGroupCode,
      statusInfo.CandidateNumber,
      userSessionData.userId,
      await getClientIp()
    );
  }

  return new Response();
}
