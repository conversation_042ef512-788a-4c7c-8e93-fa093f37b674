import { getAppInsightsServer } from "./appInsightsServer";
import { SubjectCodeFileSizesError } from "./exceptionTypes";
import { getPgsaAccessToken } from "./getPgsaAccesstoken";
import { getValueFromRedis, setValueInRedis } from "./redisHelper";

const PgsaApiUrl = process.env.PGSA_API_URL;
const redisKey = "PGS:SubjectCodeFileSizes";

const telemetryClient = getAppInsightsServer();

export async function getSubjectCodeFileSizes(): Promise<
  ISubjectCodeFileSizes[]
> {
  let fileSizeResponse: Response | null = null;
  try {
    const redisResponse = await getValueFromRedis(redisKey);

    if (redisResponse) {
      return JSON.parse(redisResponse) as ISubjectCodeFileSizes[];
    }

    const accessToken = await getPgsaAccessToken();
    fileSizeResponse = await fetch(`${PgsaApiUrl}/api/utils/filesize`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      cache: "no-store",
    });

    const fileSizeData: ISubjectCodeFileSizes[] = await fileSizeResponse.json();

    if (!fileSizeResponse.ok) {
      throw new SubjectCodeFileSizesError();
    }

    await setValueInRedis(redisKey, JSON.stringify(fileSizeData), 600);

    return fileSizeData;
  } catch (error) {
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        Message: "Error in getSubjectCodeFileSizes",
        Response: fileSizeResponse
          ? await fileSizeResponse.text()
          : "Tom respons",
        Statuscode: fileSizeResponse
          ? fileSizeResponse.status
          : "Tom statuskode",
      },
    });
    if (error instanceof Error) {
      console.error("Kunne ikke hente informasjon om filstørrelse", error);
    }
    throw new Error("Kunne ikke hente informasjon om filstørrelse");
  } finally {
  }
}
