import { getServerSession } from "next-auth";
import { authOptions } from "../api/auth/authOptions";
import { IUserSessionData } from "../interfaces/IUserSessionData";

export async function getUserSessionData(): Promise<IUserSessionData> {
  const session: any = await getServerSession(authOptions);

  var profile: IUserSessionData = {
    name: session?.user.userInfo.name,
    uid: session?.user.userInfo.uid,
    language: session?.user.userInfo.language,
    userId: session?.user.userInfo.userId,
    candidateNumber: session?.user.userInfo.candidateNumber,
    candidateGroupCode: session?.user.userInfo.candidateGroupCode,
    dayCode: session?.user.userInfo.dayCode,
    userSessionId: session?.user.userInfo.userSessionId,
    isNewAuthentication: false,
    id: "",
  };

  return profile;
}
