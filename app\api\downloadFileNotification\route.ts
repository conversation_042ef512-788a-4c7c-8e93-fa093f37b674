import { getAppInsightsServer } from "@/app/lib/appInsightsServer";
import { getClientIp } from "@/app/lib/getClientIp";
import { getPgsaAccessToken } from "@/app/lib/getPgsaAccesstoken";
import { getUserSessionData } from "@/app/lib/getUserSessionData";
import { getHashFromRedis } from "@/app/lib/redisHelper";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import { NextResponse } from "next/server";

// Adding interfaces that were implicit in the original code
interface IDownloadedFileInfo {
  DocumentCode: string;
  ContainerName: string;
}

interface IDownloadedFileNotification {
  IpAddress: string;
  DocumentCode: string;
  ContainerName: string;
}

dayjs.extend(utc);
const PgsaApiUrl = process.env.PGSA_API_ASYNC_URL;
const telemetryClient = getAppInsightsServer();

export async function POST(request: Request) {
  let response: Response | undefined;
  const downloadedFileInfo: IDownloadedFileInfo = await request.json();

  try {
    const userSessionData = await getUserSessionData();
    if (!userSessionData?.userSessionId)
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });

    // Use Redis hash to get only the isAuthorized field
    const userKey = `candidate:${userSessionData.candidateNumber}:${userSessionData.userSessionId}`;
    const isAuthorized = await getHashFromRedis(userKey, "isAuthorized");

    if (isAuthorized !== "true")
      return NextResponse.json(
        { message: "User not authorized" },
        { status: 403 }
      );

    const [pgsaAccessToken, clientIp] = await Promise.all([
      getPgsaAccessToken(),
      getClientIp(),
    ]);

    const downloadedFile: IDownloadedFileNotification = {
      IpAddress: clientIp,
      DocumentCode: downloadedFileInfo.DocumentCode,
      ContainerName: downloadedFileInfo.ContainerName,
    };

    response = await fetch(`${PgsaApiUrl}/api/exampaper/status/downloaded`, {
      method: "POST",
      body: JSON.stringify(downloadedFile),
      headers: {
        Authorization: `Bearer ${pgsaAccessToken}`,
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      throw new Error(
        `Feil ved sending av fil nedlasting notifikasjon. Feilkode: ${response.status}, Feilmelding: ${response.statusText} `
      );
    }

    return new Response();
  } catch (error) {
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        action: "downloadFileNotification",
        response: response ? await response.text() : "Tom respons",
        downloadedFileInfo,
      },
    });
    console.error(error);
    return new NextResponse("An error occurred", { status: 500 });
  }
}
