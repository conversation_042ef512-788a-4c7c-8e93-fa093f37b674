import Link from "next/link";
import { RouteEnum } from "../enums/RouteEnum";
import { getTranslations } from "next-intl/server";

export default async function NotFound() {

  const t = await getTranslations("Feil");

  return (
    <div className="mx-auto my-28 flex flex-col gap-6">
      <h1 className="text-5xl font-bold">{t("PageNotFound")}</h1>
      <p>{t("PageNotFoundText")}</p>
      <Link href={RouteEnum.Hjem}>{t("GoToHomePage")}</Link>
    </div>
  );
}
