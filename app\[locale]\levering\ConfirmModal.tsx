interface ConfirmDeleteModalProps {
  isOpen: boolean;
  fileName: string;
  onClose: () => void;
  onConfirm: () => void;
  confirmDeleteHeading: string;
  confirmDeleteMessage: string;
  confirmDeleteConfirmBtnLabel: string;
  cancelLabel: string;
}

function ConfirmDeleteModal({
  isOpen,
  onClose,
  onConfirm,
  fileName,
  confirmDeleteHeading,
  confirmDeleteMessage,
  confirmDeleteConfirmBtnLabel,
  cancelLabel: cancelLabel,
}: ConfirmDeleteModalProps) {
  if (!isOpen) return null;

  return (
    <div className="modal modal-open">
      <div className="modal-box rounded">
        <h3 className="font-bold text-lg">{confirmDeleteHeading}</h3>
        <p className="py-4 break-words">
          <span>{confirmDeleteMessage}:</span>
          <span className="font-semibold ml-2">{fileName}</span> ?
        </p>
        <div className="modal-action">
          <button onClick={onConfirm} className="btn btn-primary text-white">
            {confirmDeleteConfirmBtnLabel}
          </button>
          <button onClick={onClose} className="btn">
            {cancelLabel}
          </button>
        </div>
      </div>
    </div>
  );
}

export default ConfirmDeleteModal;
