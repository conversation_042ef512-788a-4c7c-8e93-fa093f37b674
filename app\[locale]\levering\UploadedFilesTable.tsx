"use client";

import React from "react";
import dayjs from "dayjs";
import { Fa<PERSON>he<PERSON>, FaEyeSlash, FaTimesCircle } from "react-icons/fa";
import FileActionButton from "./FileActionButtons";
import { useUploadedFiles } from "./UploadedFilesContext";
import { Skeleton } from "@/components/ui/skeleton";
import { IDeliveredFile } from "@/app/interfaces/IUploadFile";


interface UploadedFilesTableProps {
  deleteLabel: string;
  downLoadLabel: string;
  UploadDateLabel: string;
  SizeLabel: string;
  errorLoadFilesMessage: string;
  uploadFailedLabel: string;
  notUniqueFilnameLabel: string;
  filesizeTooBigLabel: string;
  fileNotAllowedLabel: string;
  checkedLabel: string;
  notCheckedLabel: string;
  confirmDeleteHeading: string;
  confirmDeleteMessage: string;
  confirmDeleteConfirmBtn: string;
  cancelLabel: string;
  deleteFailedLabel: string;
  downloadFailedLabel: string;
  emptyFileLabel: string;
}

const UploadedFilesTable: React.FC<UploadedFilesTableProps> = ({
  deleteLabel: deleteTxt,
  downLoadLabel: downLoadTxt,
  UploadDateLabel: UploadDateTxt,
  SizeLabel: SizeTxt,
  errorLoadFilesMessage,
  uploadFailedLabel,
  notUniqueFilnameLabel,
  filesizeTooBigLabel,
  fileNotAllowedLabel,
  checkedLabel,
  notCheckedLabel,
  confirmDeleteHeading,
  confirmDeleteMessage,
  confirmDeleteConfirmBtn,
  cancelLabel,
  deleteFailedLabel,
  downloadFailedLabel,
  emptyFileLabel,
}) => {
  const { uploadedFiles, fetchBlobsError, loadingBlobs } =
    useUploadedFiles();

  function formatFileSize(bytes: number): string {
    const KB = 1024;
    const MB = 1024 * KB;
    return bytes < MB
      ? `${(bytes / KB).toFixed(0)} KB`
      : `${(bytes / MB).toFixed(2)} MB`;
  }

  const renderErrorBadge = (label: string) => (
    <span className="badge badge-error p-5 w-48 text-white">{label}</span>
  );

  function renderStatus(file: IDeliveredFile): React.JSX.Element {
    if (!file.uploadFinished) {
      return (
        <span className="badge badge-ghost p-5 w-48">
          <span className="loading loading-bars loading-md"></span>
        </span>
      );
    }

    switch (file.error) {
      case "file-invalid-type":
        return renderErrorBadge(fileNotAllowedLabel);
      case "file-too-large":
        return renderErrorBadge(filesizeTooBigLabel);
      case "file-conflicting-name":
        return renderErrorBadge(notUniqueFilnameLabel);
      case "upload-failed":
        return renderErrorBadge(uploadFailedLabel);
      case "delete-failed":
        return renderErrorBadge(deleteFailedLabel);
      case "download-failed":
        return renderErrorBadge(downloadFailedLabel);
      case "file-empty":
        return renderErrorBadge(emptyFileLabel);
      default:
        return (
          <span
            className={`badge ${
              file.checked ? "badge-success" : "badge-ghost"
            } p-5 w-full sm:w-48`}
          >
            <span className="flex items-center gap-2">
              <span className="text-base">
                {file.checked ? (
                  <FaCheck role="img" aria-label="checked" />
                ) : (
                  <FaEyeSlash role="img" aria-label="preview-icon" />
                )}
              </span>
              {file.checked ? checkedLabel : notCheckedLabel}
            </span>
          </span>
        );
    }
  }

  if (fetchBlobsError) {
    return (
      <div className="flex gap-4 items-center p-4 border border-red-600 bg-white text-red-600">
        <FaTimesCircle role="img" aria-label="error-icon" />
        <span>{errorLoadFilesMessage}</span>
      </div>
    );
  }

  if (loadingBlobs) {
    return (
      <>
        <div className="hidden md:flex border w-full p-4 border-neutral-400 rounded-lg">
          <div className="flex justify-between w-full">
            <div className="flex gap-8 items-center">
              <Skeleton className="w-48 h-10 rounded-lg bg-base-300" />
              <Skeleton className="w-96 h-6 rounded-md bg-base-300" />
            </div>
            <div className="flex gap-4 items-center">
              <Skeleton className="w-24 h-10 bg-base-300" />
              <Skeleton className="w-24 h-10 bg-base-300" />
            </div>
          </div>
        </div>
        <div className="md:hidden border border-neutral-400 w-full p-6">
          <div className="flex flex-col gap-4">
            <Skeleton className=" w-full h-6 bg-base-300" />
            <Skeleton className=" w-48 h-10 bg-base-300" />
            <Skeleton className=" w-32 h-4 bg-base-300" />
            <Skeleton className=" w-40 h-4 bg-base-300" />
            <div className="flex gap-4">
              <Skeleton className=" w-24 h-10 bg-base-300" />
              <Skeleton className=" w-24 h-10 bg-base-300" />
            </div>
          </div>
        </div>
      </>
    );
  }

  return (
    <div>
      {/* Flexbox layout for medium and larger screens */}
      <div className="hidden md:flex flex-col w-full gap-1">
        {uploadedFiles.map((file) => (
          <div
            key={file.fileGuid}
            className="flex bg-white p-3 rounded shadow gap-5 items-center"
          >
            <div className="flex-none">{renderStatus(file)}</div>
            <div
              className="flex-grow text-left break-all"
              title={file.fileName}
            >
              {file.fileName}
            </div>
            <div className="hidden lg:flex justify-end whitespace-nowrap w-24">
              {formatFileSize(file.size)}
            </div>
            <div className="hidden lg:flex justify-end whitespace-nowrap w-14">
              {dayjs(file.uploadDate).format("HH:mm")}
            </div>
            <div className="flex-none flex justify-end gap-2 w-48">
              <FileActionButton
                deleteLabel={deleteTxt}
                downLoadLabel={downLoadTxt}
                file={file}
                confirmDeleteHeading={confirmDeleteHeading}
                confirmDeleteMessage={confirmDeleteMessage}
                confirmDeleteConfirmBtn={confirmDeleteConfirmBtn}
                cancelLabel={cancelLabel}
              />
            </div>
          </div>
        ))}
      </div>
      {/* Card layout for small screens */}
      <div className="md:hidden">
        {uploadedFiles.map((file) => (
          <div key={file.fileGuid} className="card mb-4 shadow-md">
            <div className="card-body card-bordered bg-white rounded overflow-hidden">
              <h2
                className="card-title break-all overflow-hidden text-ellipsis"
                title={file.fileName}
              >
                {file.fileName}
              </h2>
              <div className="flex flex-col gap-2">
                <div>{renderStatus(file)}</div>
                <div className="flex flex-col sm:flex-row sm:gap-2">
                  <span>{SizeTxt}:</span>
                  <span>{formatFileSize(file.size)}</span>
                </div>
                <div className="flex flex-col sm:flex-row sm:gap-2">
                  <span>{UploadDateTxt}:</span>
                  <span>{dayjs(file.uploadDate).format("HH:mm")}</span>
                </div>
                <FileActionButton
                  deleteLabel={deleteTxt}
                  downLoadLabel={downLoadTxt}
                  file={file}
                  confirmDeleteHeading={confirmDeleteHeading}
                  confirmDeleteMessage={confirmDeleteMessage}
                  confirmDeleteConfirmBtn={confirmDeleteConfirmBtn}
                  cancelLabel={cancelLabel}
                />
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default UploadedFilesTable;
