1. <PERSON><PERSON><PERSON><PERSON> terraform script som skal opprette sql db, service bus og logic appen. 

2. <PERSON>psett managed identity mellom PGSK og service busen. 
    Gå til service busen: 
       Gi Azure Service Bus Data Sender for wa-pgs-next-{miljø}

3. Oppsett managed identity mallom service bus og logic appen.
   Gå til service busen: 
       Gi Azure Service Bus Data Reciever for logic-pgs-db-{miljø}


4. <PERSON><PERSON>tt managed identity mallom logicappen og sql server.
   1. Enable system assigned managed identity on logic-pgs-db-{env}
       Gå til query editor, ikke velge noen databaser og kjør følgende kommondoer: 

       CREATE USER [logic-pgs-db-test] FROM EXTERNAL PROVIDER;
       ALTER ROLE db_datareader ADD MEMBER [logic-pgs-db-test];
       ALTER ROLE db_datawriter ADD MEMBER [logic-pgs-db-test];


    2. Enable system assigned managed identity on wa-pgs-admin-{env}
        Run following command in sql
        CREATE USER [wa-pgs-admin-dev] FROM EXTERNAL PROVIDER;
        ALTER ROLE db_datareader ADD MEMBER [wa-pgs-admin-dev];


5. Add workflow in logic app 
    1. Create workflow called "wf-log-activity" and add two parameters there "PgsDbServer", "PgsDbName"
    2. Copy worflow from other environemt. Fix connections










