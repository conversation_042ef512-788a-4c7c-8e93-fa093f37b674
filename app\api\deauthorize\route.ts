// route.ts
import { getUserSessionData } from "@/app/lib/getUserSessionData";
import { deauthorizeUserObjectRedis } from "@/app/lib/deauthorizeUserObjectRedis";
import { NextResponse } from "next/server";

export async function POST(request: Request) {
  try {
    // Hent og valider brukersesjonsdata
    const userSessionData = await getUserSessionData();
    if (!userSessionData?.userSessionId || !userSessionData.candidateNumber) {
      return NextResponse.json(
        { message: "Manglende brukerdata" },
        { status: 401 }
      );
    }

    // Parse request body for optional sessionId
    let specificSessionId: string | undefined;
    try {
      const body = await request.json();
      specificSessionId = body?.sessionId;
    } catch {
      // If no body or invalid JSON, continue with default behavior
    }

    // Utfør Redis-oppdateringen
    const result = await deauthorizeUserObjectRedis(
      userSessionData.candidateNumber,
      specificSessionId
    );

    return NextResponse.json(
      {
        message: result,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Feil ved oppdatering av autorisasjonsstatus:", error);
    return NextResponse.json(
      {
        message: "En feil oppstod ved oppdatering av autorisasjonsstatus",
        error: error instanceof Error ? error.message : "Ukjent feil",
      },
      { status: 500 }
    );
  }
}
